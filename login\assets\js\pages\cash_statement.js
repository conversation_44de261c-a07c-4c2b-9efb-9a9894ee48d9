
var treeDataSource;
var sAjaxSourceUrl = "cash_statement.aspx/";


var optionsLoading = {
    top: 100,
    autoOpen: true,
    overlayOpacity: 0.3,
    overlayColor: "#333",
    overlayClose: false,
    closeOnEscape: false
};



$(document).ready(function () {
    LaunchTraduction();
    LoadPage();
});


var sessionsList = new Array();


function LoadPage() {



    var currentdate = new Date();
    var startDatetime = getLocaleShortDateString(currentdate) + " 00:00";
    var endDatetime = getLocaleShortDateString(currentdate) + " " + currentdate.timeNow();


    ////            var currentdate = new Date();
    ////            currentdate.toLocaleString();

    ////            var startDatetime = currentdate.today() + " 00:00";
    ////            var endDatetime = currentdate.today() + " " + currentdate.timeNow();

    var lang = $("#dateLang").val();
    //convertit le format de date
    $.datepicker.setDefaults($.datepicker.regional[lang]);


    $('#toDate').val(endDatetime);
    $('#fromDate').val(startDatetime);

    //$('#toDate, #toEventDate').val(endDatetime);
    // $('#fromDate, #fromEventDate').val(startDatetime);


    // $('#toEventDate').datetimepicker();



    $('#toDate').datetimepicker({
        controlType: myControl
    });

    $('#fromDate').datetimepicker({
        controlType: myControl
    });



    $("#id-check-horizontal").on('change', function () {
        if ($("#optionDates input").prop('disabled')) {

            // $("#id-check-horizontal").parent().find("small").find("b").html("Activer")
            $("#optionDates input[type='text']").prop("disabled", false);
        } else {

            //$("#id-check-horizontal").parent().find("small").find("b").html("Désactiver")
            $("#optionDates input[type='text']").prop("disabled", true);
        }
    }); // fin on change


    var isFutureSession = $("#id-check-futuresessions").prop('checked')
    var startEventDate = $('#fromEventDate').val()
    var endEventDate = $('#toEventDate').val()


    loadEventsGroups(isFutureSession, startEventDate, endEventDate)


    $("#id-check-futuresessions").on('change', function (e) {
        var isFutureSession = $(this).prop('checked')
        var strStartEventDate = $('#fromEventDate').val()
        var strEndEventDate = $('#toEventDate').val()


        var dtStartEventDate = '';
        var dtEndEventDate = '';
        if (strStartEventDate != '' && strEndEventDate != '') {
            var startEventDate = new Date(strStartEventDate);
            var endEventDate = new Date(strEndEventDate);

            dtStartEventDate = startEventDate.getDate() + '-' + (startEventDate.getMonth() + 1) + '-' + startEventDate.getFullYear()
            dtEndEventDate = endEventDate.getDate() + '-' + (endEventDate.getMonth() + 1) + '-' + endEventDate.getFullYear()

        }


        loadEventsGroups(isFutureSession, dtStartEventDate, dtEndEventDate)
        //loadEventsGroups(isFutureSession, startEventDate, endEventDate)
    }); // fin on change


    $('#btnEventsDatesValid').on('click', function (e) {
        var isFutureSession = $("#id-check-futuresessions").prop('checked')
        var strStartEventDate = $('#fromEventDate').val()
        var strEndEventDate = $('#toEventDate').val()


        var dtStartEventDate = '';
        var dtEndEventDate = '';
        if (strStartEventDate != '' && strEndEventDate != '') {
            var startEventDate = new Date(strStartEventDate);
            var endEventDate = new Date(strEndEventDate);

            dtStartEventDate = startEventDate.getDate() + '-' + (startEventDate.getMonth() + 1) + '-' + startEventDate.getFullYear()
            dtEndEventDate = endEventDate.getDate() + '-' + (endEventDate.getMonth() + 1) + '-' + endEventDate.getFullYear()

        }


        loadEventsGroups(isFutureSession, dtStartEventDate, dtEndEventDate)
    });



    $('#btn_select_manifs').on('click', function () {
        $('input[name=chkEventName]').click();
    });

    $('#select_all_prices').on('click', function () {
        if ($(this).data("state") == "uncheck") {
            $(this).text(ReadXmlTranslate("lbl_check_prices"))
            $(this).data("state", "check");
        } else {
            $(this).text(ReadXmlTranslate("lbl_uncheck_prices"))
            $(this).data("state", "uncheck");
        }

        $('input[name=chkTarifID]').click();
    });

    $("#btnPdfExport").on('click', function () {
        var urlpage = getAbsolutePath().substring(0, getAbsolutePath().lastIndexOf('/') - 5);

        var _resume = $('#resume').html();
        var _tables = $('.dataTables').html();

        var sData = JSON.stringify({ resume: _resume, tables: _tables });

        $.ajax({
            type: "POST",
            url: sAjaxSourceUrl + 'DownloadPDFFile',
            data: sData,
            contentType: 'application/json; charset=utf-8',
            dataType: "json",
            success: function (response) {
                window.open(window.location.origin + response.d, response.d, 'width=1024,height=768,toolbar=no,location=no,directories=no,status=no,menubar=no,copyhistory=no');
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
                console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
            },
            complete: function () {
                $("#loadingmodal").trigger('closeModal');
                $('#modalMessage').modal('hide');
            }
        }); // fin click DownloadPDFFile
    }); //fin  click btnPdfExport

    $("#btnExcelExport").on('click', function (e) {
        var urlpage = getAbsolutePath().substring(0, getAbsolutePath().lastIndexOf('/') - 5);

        var _resume = $('#resume').html();
        var _tables = $('.dataTables').html();


        var sData = JSON.stringify({ resume: _resume, tables: _tables });


        $.ajax({
            type: "POST",
            url: sAjaxSourceUrl + 'DownloadExcelFile',
            data: sData,
            contentType: 'application/json; charset=utf-8',
            dataType: "json",
            beforeSend: function () {
                //                var html = ' <img src="../assets/images/loader.gif" alt="loader" />';
                //                $('#loading').html(html + $('#lblLoadingChargementEnCours').text());
                $("#loadingmodal").easyModal(optionsLoading);
            },
            success: function (response) {
                $('#loading').html('');


                //                if (isNaN(response.d) == false) {
                //                    $('#iframe').attr('src', 'Export.aspx?ExcelReportId=' + response.d);
                //                    $('#iframe').load();
                //                }
                //                else {
                //                    alert(response.d);
                //                }
                //  window.open(urlpage + '/excels/' + response.d);
                window.open(window.location.origin + response.d, response.d, 'width=1024,height=768,toolbar=no,location=no,directories=no,status=no,menubar=no,copyhistory=no');
                // window.open("excels/" + response.d, response.d, 'width=1024,height=768,toolbar=no,location=no,directories=no,status=no,menubar=no,copyhistory=no')
                //  window.open("Export.aspx", "Export CSV", "width=120,height=300");
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
                console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
            },
            complete: function () {
                $("#loadingmodal").trigger('closeModal');
                $('#modalMessage').modal('hide');
            }
        }); // fin click DownloadExcelFile



    });

    $("#btnExcelExportcelleci").on('click', function (e) {

        var urlpage = getAbsolutePath().substring(0, getAbsolutePath().lastIndexOf('/') - 5);

        var _resume = $('#resume').html();
        var _tables = $('.dataTables').html();


        var sData = JSON.stringify({ resume: _resume, tables: _tables });

        $.ajax({
            type: "POST",
            url: sAjaxSourceUrl + 'DownloadExcelFile',
            data: sData,
            contentType: 'application/json; charset=utf-8',
            dataType: "json",
            beforeSend: function () {
                //                var html = ' <img src="../assets/images/loader.gif" alt="loader" />';
                //                $('#loading').html(html + $('#lblLoadingChargementEnCours').text());
                $("#loadingmodal").easyModal(optionsLoading);
            },
            success: function (response) {
                $('#loading').html('');


                //                if (isNaN(response.d) == false) {
                //                    $('#iframe').attr('src', 'Export.aspx?ExcelReportId=' + response.d);
                //                    $('#iframe').load();
                //                }
                //                else {
                //                    alert(response.d);
                //                }
                window.open(urlpage + '/excels/' + response.d);
                // window.open("excels/" + response.d, response.d, 'width=1024,height=768,toolbar=no,location=no,directories=no,status=no,menubar=no,copyhistory=no')
                //  window.open("Export.aspx", "Export CSV", "width=120,height=300");
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
                console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
            },
            complete: function () {
                $("#loadingmodal").trigger('closeModal');
                $('#modalMessage').modal('hide');
            }
        }); // fin click DownloadExcelFile

    });


    $("#btnExcelExportgood").on('click', function (e) {

        var _resume = $('#resume').html();
        var _tables = $('.dataTables').html();



        var html = "";
        var resume = "";
        $("#resume div.widget-main p ").each(function (i, item) {
            resume += item.textContent.sansAccent() + "\n";
        });

        html += resume + " \n \n ";
        $('.dataTables div.table-responsive table').each(function (i, item) {
            var head = item.tHead;
            var body = item.tBodies;
            var foot = item.tFoot;

            html += head.textContent.sansAccent() + " " + body[0].textContent.sansAccent() + " " + foot.textContent.sansAccent();
            html += "\n";
        });



        //        $('#dataTables').tableExport({ type: 'excel', escape: 'false' }); 
        $('body').prepend("<form method='post' action='exportPage.aspx' style='top:-3333333333px;' id='tempForm'><input type='hidden' name='data' value='" + html + "' ></form>");
        $('#tempForm').submit();
        $("tempForm").remove();
        //        var ua = window.navigator.userAgent;
        //        var msie = ua.indexOf("MSIE ");
        //        if (msie > 0 || !navigator.userAgent.match(/Trident.*rv\:11\./)) {

        //          
        //            //            
        //        } else {
        //                window.open('data:application/vnd.ms-excel;charset=utf-8;,' + encodeURIComponent(html));
        //        }

        //  window.open("data:text/csv;charset=utf-8;base64," + $.base64Encode(html));
        //  window.open('data:application/vnd.ms-excel,' + encodeURIComponent(html));
        //  window.open('data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,' + encodeURIComponent(html));
        e.preventDefault();

    });


}//Fin load page


function loadEventsGroups(bIsFutureSessions, strStartEventsDate, strEndEventsDate) {
    $('#Eventstree').html('');

    var sData = JSON.stringify({
        isFutureSessions: bIsFutureSessions,
        startEventsDate: strStartEventsDate,
        endEventsDate: strEndEventsDate
    });

    $.ajax({
        type: "POST",
        url: sAjaxSourceUrl + 'GetEventsData',
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        data: sData,
        beforeSend: function () {
            //            var html = ' <img src="../assets/images/loader.gif" alt="loader" />';
            //            $('#loading').html(html + $('#lblLoadingChargementEnCours').text());
            $("#loadingmodal").easyModal(optionsLoading);
        },
        success: function (response) {
            //  $('#loading').html('' );
            var parsed = $.parseJSON(response.d);
            sessionsList = parsed;

            var htmlUl = "<input type='text' class='search-input' autocomplete='off' id='id_search_list' placeholder='" + ReadXmlTranslate('lbl_search') + "'><br />";
            htmlUl += "<ul id='tree' > "

            $.each(parsed, function (i, jsondata) {
                //parents
                htmlUl += "<li> <input type='checkbox' class='ace_custom' idevent='" + jsondata.EventId + "' value='" + jsondata.EventName + "' name='chkEventName' /><label class='parent'> <span class='lbl'>" + jsondata.EventName + "</span> <i class='icon-plus-sign'></i></label> ";
                if (jsondata.ListSessions.length > 0) {
                    // enfants
                    htmlUl += "<ul  class='child' idevent='" + jsondata.EventId + "'>";
                    $.each(jsondata.ListSessions, function (li, dataSession) {
                        htmlUl += "<li><label > <input name='chkSession' type='checkbox' class='ace' idevent='" + dataSession.EventId + "' value='" + dataSession.SessionId + "' /> <span class='lbl'>" + dataSession.sSessionStartDate + "</span> </label>  </li>";
                    });
                    htmlUl += "</ul>";
                }

                htmlUl += "</li>";
                var x = jsondata;
            });

            htmlUl += "</ul>";

            $('#Eventstree').append(htmlUl);
            $('ul#tree').checktree();

            $('.child').hide();
            $('label.parent').on("click", function () {
                if ($(this).hasClass("plusimageapply")) {
                    $(this).removeClass('plusimageapply');
                    $(this).parent().find('ul').hide();
                    $(this).children('i').removeClass('icon-minus-sign');
                    $(this).children('i').addClass('icon-plus-sign');
                } else {
                    // $(this).css('list-style-image',
                    $(this).addClass('plusimageapply');
                    $(this).parent().find('ul').show();
                    $(this).children('i').addClass('icon-minus-sign');
                    $(this).children('i').removeClass('icon-plus-sign');
                }
            }); // fin du click



            // var qs = $('input#id_search_list').quicksearch('ul#tree li');
            var qs = $('input#id_search_list').quicksearch('ul#tree li',
                {
                    'show': function () {
                        //$('label.parent').parent().addClass('show');
                        $(this).addClass('show');
                    },
                    'hide': function () {
                        //                                   // $('label.parent').parent().addClass('hide');
                        //                                    $('label.parent').parent().removeClass('show');
                        $(this).addClass('hide');
                        $(this).removeClass('show');

                        $(this).children().removeClass('hide')
                        $('ul.child').each(function (i, itm) {
                            $(this).children().removeClass('hide')

                        });
                    },
                    'prepareQuery': function (val) {
                        return new RegExp(val, "i");
                    },
                    'testQuery': function (query, txt, _row) {
                        // console.log(_row);
                        return query.test(txt);
                    }

                });


            //Permets la multi selection sur les chekbox en appuyant sur shift (a la Gmail)
            //                    if($('input[name=chkSession]').length > 0)
            //                        $('input[name=chkSession]').shiftcheckbox();
            $('#tree').checkboxes('range', true);

        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        },
        complete: function () {
            $("#loadingmodal").trigger('closeModal');
            $('#modalMessage').modal('hide');
        }
    });
}


//récupère les checbox selectionnés des manifestations 0 pour tout
function GetEventsNameSelected() {
    var chkEventsName = new Array();

    $('input[name=chkEventName]:checked').each(function () {
        chkEventsName.push(this.value);
    });

    return chkEventsName;
}

//récupère les sessionsID selectionnés
function GetSessionsIDSelected() {
    var selectedchkSessionID = new Array();

    $('input[name=chkSession]:checked').each(function () {
        selectedchkSessionID.push(this.value);
    });
    return selectedchkSessionID;
}

//Récupère les noms de tarifs 0 pour tout récupérer
function GetTarifNameSelected(nb) {
    var arrTarifEvent = new Array();

    if (nb == 0) {

        $('input[name="chkTarifID"]:checked').each(function () {
            arrTarifEvent.push(this.attributes.tarifname.value);
            // console.log(this.value);
        });
    } else {
        $('input[name="chkTarifID"]:checked').each(function (i, itm) {
            if (i <= nb) {
                arrTarifEvent.push(this.attributes.tarifname.value);
            }
            // console.log(this.value);
        });
    }
    return arrTarifEvent;
}

//Récupère les ID tarifs
function GetTarifIDSelected() {

    var arrTarifEvent = new Array();

    $('input[name="chkTarifID"]:checked').each(function () {
        arrTarifEvent.push(this.value);
        // console.log(this.value);
    });

    return arrTarifEvent;
}

function GetEtatSelected() {

    var arrEtat = new Array();

    $('input[name="etat"]:checked').each(function () {
        arrEtat.push(this.value);
        console.log(this.value);
    });

    return arrEtat;
}

//Récupère le nom des tarifs
function GetTarifsManifs() {
    var sessionIdSelected = GetSessionsIDSelected();
    var sData = JSON.stringify({ _sessionId: sessionIdSelected });

    $.ajax({
        type: "POST",
        url: sAjaxSourceUrl + 'GetTarifsManifs',
        data: sData,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        beforeSend: function () {
            //            var html = ' <img src="../assets/images/loader.gif" alt="loader" />';
            //            $('#loading').html(html + $('#lblLoadingChargementEnCours').text());
            $("#loadingmodal").easyModal(optionsLoading);
        },
        success: function (response) {
            $('#loading').html('');
            var parsed = $.parseJSON(response.d);
            var htmlCheck = "";
            $.each(parsed, function (i, jsondata) {
                htmlCheck = htmlCheck + "<div class='checkbox'><label><input name='chkTarifID' tarifName='" + jsondata.type_tarif_nom + "' checked type='checkbox' class='ace' value='" + jsondata.type_tarif_id + "' /> <span class='lbl'> " + jsondata.type_tarif_nom + "</span> </label> </div>";
            });

            $("#tarifManifs").html(htmlCheck);
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        },
        complete: function () {
            $("#loadingmodal").trigger('closeModal');
            $('#modalMessage').modal('hide');
        }
    });
}


function GetSessionState() {
    var _startDate = $('#fromDate').val();
    var _endDate = $('#toDate').val();


    if ($("#optionDates input[type='text']").prop('disabled')) {
        _startDate = "";
        _endDate = "";
    }

    var sessionIdSelected = GetSessionsIDSelected();
    var tarifIdSelected = GetTarifIDSelected();
    var eventNameSelected = GetEventsNameSelected();
    var etatSelected = GetEtatSelected()

    // var sData = JSON.stringify({ sessionsId: sessionIdSelected, tarifId: tarifIdSelected, eventsName: eventNameSelected, startDate: _startDate, endDate: _endDate, userLanguage: _userLanguage });
    var sData = JSON.stringify({ sessionsId: sessionIdSelected, tarifId: tarifIdSelected, eventsName: eventNameSelected, startDate: _startDate, endDate: _endDate, _lstEtatsName: etatSelected });

    $.ajax({
        type: "POST",
        url: sAjaxSourceUrl + 'GetSessionState',
        data: sData,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        beforeSend: function () {
            //            var html = ' <img src="../assets/images/loader.gif" alt="loader" />';
            //            $('#loading').html(html + $('#lblCalculCashStatement').text());
            //$("#loadingmodal").easyModal(optionsLoading);
            $('#btnNext').attr('disabled', 'disabled');
        },
        success: function (response) {
            // $('#loading').html('');
            // ShowError("error", response.d.ERROR, "alert alert-success alert-dismissable");

            if (response.d.substring(0, 3) == "OK:") {
                $("#resume").html("");

                var htmlResume = " <div class='widget-box'><div class='widget-header header-color-blue2'><h4 class='lighter white'>" + ReadXmlTranslate('resume_select_previous') + "</h4></div>";
                htmlResume += "<div class='widget-body'><div class='widget-main'>";
                htmlResume += "<p><span class='bold'>" + ReadXmlTranslate('resume_select_manifestations') + " </span>" + GetEventsNameSelected().join(', ') + "</p>";
                htmlResume += "<p><span class='bold'>" + ReadXmlTranslate('resume_select_tarifs') + " </span>" + GetTarifNameSelected(5).join(', ') + "</p>";

                if ($("#id-check-horizontal:checked").length > 0) {
                    htmlResume += "<p><label class='bold'>" + ReadXmlTranslate('resume_select_date') + "</label> <span>" + ReadXmlTranslate('select_date').replace('[startDate]', $("#fromDate").val()).replace('[endDate]', $("#toDate").val()) + " </span> </p>";
                } else {
                    htmlResume += "<p><label class='bold'>" + ReadXmlTranslate('resume_select_date') + " </label> <span>" + ReadXmlTranslate('aucun_filtre') + "</span></p>";
                }
                htmlResume += "</div></div><div class='space'></div>";

                $("#resume").html(htmlResume);
                $("#resume").show();

                var htmlFinal = response.d.substring(3);
                // Ajoute les tableaux créer en c#
                $('.dataTables').html(htmlFinal);


                LaunchTraduction();

                // Forcer le rechargement des traductions si nécessaire
                if (typeof ReloadTranslations === 'function') {
                    ReloadTranslations();
                }

                // Dictionnaire de traductions de secours
                var fallbackTranslations = {
                    'reference_unique_billet': 'Référence unique du billet',
                    'dernier_etat_place': 'Dernier état place',
                    'id_filiere_vente': 'ID filière vente',
                    'nom_filiere_vente': 'Nom filière vente',
                    'id_manifestation': 'ID manifestation',
                    'nom_manifestation': 'Nom manifestation',
                    'groupe_manifestations': 'Groupe manifestations',
                    'genre_manifestation': 'Genre manifestation',
                    'sous_genre_manifestation': 'Sous-genre manifestation',
                    'cible_manifestation': 'Cible manifestation',
                    'id_seance': 'ID séance',
                    'date_debut_seance': 'Date début séance',
                    'id_categorie': 'ID catégorie',
                    'nom_categorie': 'Nom catégorie',
                    'id_tarif': 'ID tarif',
                    'nom_tarif': 'Nom tarif',
                    'numero_commande': 'Numéro commande',
                    'mode_paiement': 'Mode paiement',
                    'date_operation': 'Date opération',
                    'id_identite': 'ID identité',
                    'nom_identite': 'Nom identité',
                    'prenom_identite': 'Prénom identité',
                    'civilite': 'Civilité',
                    'code_postal': 'Code postal',
                    'ville': 'Ville',
                    'pays': 'Pays',
                    'date_naissance': 'Date naissance',
                    'filiere_creation_identite': 'Filière création identité',
                    'telephone_mobile': 'Téléphone mobile',
                    'adresse_postale': 'Adresse postale',
                    'opt_in': 'Opt-in',
                    'numero_billet': 'Numéro billet'
                };

                // Test de la fonction ReadXmlTranslate
                console.log("=== TEST TRADUCTION ===");
                console.log("Test reference_unique_billet:", ReadXmlTranslate('reference_unique_billet'));
                console.log("Test dernier_etat_place:", ReadXmlTranslate('dernier_etat_place'));
                console.log("Test id_filiere_vente:", ReadXmlTranslate('id_filiere_vente'));

                // Traduit les en-têtes de colonnes après injection du HTML
                setTimeout(function() {
                    console.log("=== DEBUT TRADUCTION COLONNES ===");
                    $('.dataTables table').each(function() {
                        var tableId = $(this).attr('id');
                        if (!tableId) {
                            tableId = 'cash_table_' + Math.random().toString(36).substring(2, 11);
                            $(this).attr('id', tableId);
                        }
                        console.log("Traduction du tableau:", tableId);

                        // Test manuel de traduction des en-têtes
                        $(this).find('thead td').each(function() {
                            var $cell = $(this);
                            var tradKey = $cell.attr('data-trad');
                            var currentText = $cell.text().trim();

                            console.log("=== EN-TÊTE TROUVÉ ===");
                            console.log("Texte actuel:", currentText);
                            console.log("data-trad:", tradKey);

                            if (tradKey) {
                                var translatedText = ReadXmlTranslate(tradKey);
                                console.log("Traduction trouvée:", translatedText);

                                if (translatedText && translatedText !== tradKey && translatedText !== currentText) {
                                    $cell.text(translatedText);
                                    console.log("✅ Traduit:", tradKey, "->", translatedText);
                                } else {
                                    // Utiliser le dictionnaire de secours
                                    if (fallbackTranslations[tradKey]) {
                                        $cell.text(fallbackTranslations[tradKey]);
                                        console.log("✅ Traduit avec secours:", tradKey, "->", fallbackTranslations[tradKey]);
                                    } else {
                                        console.log("❌ Pas de traduction pour:", tradKey);
                                    }
                                }
                            } else {
                                // Essayer de traduire directement par le texte
                                var translatedText = ReadXmlTranslate(currentText);
                                if (translatedText && translatedText !== currentText) {
                                    $cell.text(translatedText);
                                    console.log("✅ Traduit par texte:", currentText, "->", translatedText);
                                } else if (fallbackTranslations[currentText]) {
                                    $cell.text(fallbackTranslations[currentText]);
                                    console.log("✅ Traduit par texte avec secours:", currentText, "->", fallbackTranslations[currentText]);
                                } else {
                                    console.log("❌ Pas de data-trad et pas de traduction pour:", currentText);
                                }
                            }
                        });

                        if (typeof TranslateColumnHeaders === 'function') {
                            TranslateColumnHeaders(tableId);
                        }
                    });
                    console.log("=== FIN TRADUCTION COLONNES ===");
                }, 500);


                $('.amount').priceFormat({
                    prefix: '',
                    suffix: ' &euro;',
                    thousandsSeparator: ' '
                });



                $('button.spdetailmanifseance').on('click', function (e) {
                    e.preventDefault();

                    var manifid = $(this).closest('tr').data('eventid');
                    // var seanceid = $(this).closest('tr').data('seanceid');
                    var seanceid = $(this).closest('thead').find('tr[data-seanceid]').data('seanceid')

                    getDetailOfManifAndSeance(manifid, seanceid);
                })



                $('button.spfulldetail').on('click', function (e) {
                    e.preventDefault();

                    var manifid = $(this).closest('tr').data('eventid');
                    var seanceid = $(this).closest('thead').find('tr[data-seanceid]').data('seanceid')

                    getFullDetail(manifid, seanceid);
                })

            } else {
                ShowError("error", response.d + "\r\n", "alert alert-danger alert-dismissable", 5000);
                console.log(response.d);
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        },
        complete: function () {
            $("#loadingmodal").trigger('closeModal');
            $('#modalMessage').modal('hide');


        }
    });
}


function getFullDetail(manifid, seanceid) {

    var tarifIdSelected = GetTarifIDSelected()

    var sData = JSON.stringify({ eventId: manifid, sessionId: seanceid, _lstTarifs: tarifIdSelected });

    $.ajax({
        type: "POST",
        url: sAjaxSourceUrl + 'GetFullDetail',
        data: sData,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        beforeSend: function () {
            $('#btnNext').attr('disabled', 'disabled');
        },
        success: function (response) {

            //Tableau afficher en popup dans une variable pour pouvoir la passer en paramètre pour l'export excel
            var tablesRetour = response.d;
            ShowGenericModal('&nbsp;', response.d, '', 'xl', '', true, true)

            initSelectHeaderTab();

            // Traduire les en-têtes du tableau détaillé dans la modal
            setTimeout(function() {
                console.log("=== TRADUCTION TABLEAU DÉTAILLÉ ===");
                $('#modalGeneric table').each(function() {
                    var tableId = $(this).attr('id');
                    if (!tableId) {
                        tableId = 'detail_table_' + Math.random().toString(36).substring(2, 11);
                        $(this).attr('id', tableId);
                    }
                    console.log("Traduction du tableau détaillé:", tableId);

                    // Traduire les en-têtes du tableau détaillé
                    $(this).find('thead td').each(function() {
                        var $cell = $(this);
                        var tradKey = $cell.attr('data-trad');
                        var currentText = $cell.text().trim();

                        console.log("=== EN-TÊTE DÉTAILLÉ ===");
                        console.log("Texte actuel:", currentText);
                        console.log("data-trad:", tradKey);

                        if (tradKey) {
                            var translatedText = ReadXmlTranslate(tradKey);
                            console.log("Traduction trouvée:", translatedText);

                            if (translatedText && translatedText !== tradKey && translatedText !== currentText) {
                                $cell.text(translatedText);
                                console.log("✅ Traduit:", tradKey, "->", translatedText);
                            } else {
                                // Utiliser le dictionnaire de secours
                                if (fallbackTranslations[tradKey]) {
                                    $cell.text(fallbackTranslations[tradKey]);
                                    console.log("✅ Traduit avec secours:", tradKey, "->", fallbackTranslations[tradKey]);
                                } else {
                                    console.log("❌ Pas de traduction pour:", tradKey);
                                }
                            }
                        } else {
                            // Essayer de traduire directement par le texte
                            var translatedText = ReadXmlTranslate(currentText);
                            if (translatedText && translatedText !== currentText) {
                                $cell.text(translatedText);
                                console.log("✅ Traduit par texte:", currentText, "->", translatedText);
                            } else if (fallbackTranslations[currentText]) {
                                $cell.text(fallbackTranslations[currentText]);
                                console.log("✅ Traduit par texte avec secours:", currentText, "->", fallbackTranslations[currentText]);
                            } else {
                                console.log("❌ Pas de data-trad et pas de traduction pour:", currentText);
                            }
                        }
                    });
                });
                console.log("=== FIN TRADUCTION TABLEAU DÉTAILLÉ ===");
            }, 500);

            var selectExportDetailFormatTab = [];
            $('#selectExportDetailFormat').on('change', function () {
                selectExportDetailFormatTab = $('#selectExportDetailFormat').val()
            });



            $('#btnPdfExportDetail').on('click', function (e) {
                e.preventDefault();


                var footerhtml = '<button type="button" class="btn btn-default" data-dismiss="modal" data-trad="annuler">Annuler</button>' +
                    '<button type="button" class="btn btn-primary" id="confirmDownloadDetail">Oui, je veux télécharger</button>'

                ShowGenericModal("RGPD", 'RGPD text', footerhtml, '', '', true, true)

                //confirmer la réinitialisation
                $('#confirmDownloadDetail').off('click').click(function () {

                    var sData = JSON.stringify({ tables: tablesRetour, selectedCols: selectExportDetailFormatTab });

                    $.ajax({
                        type: "POST",
                        url: sAjaxSourceUrl + 'DownloadDetailExcelFile',
                        data: sData,
                        contentType: 'application/json; charset=utf-8',
                        dataType: "json",
                        success: function (response) {
                            $('#loading').html('');
                            window.open(window.location.origin + response.d, response.d, 'width=1024,height=768,toolbar=no,location=no,directories=no,status=no,menubar=no,copyhistory=no');
                        },
                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                            ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
                            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
                        },
                        complete: function () {
                            $('#modalGeneric').modal('hide');
                        }
                    });

                });

            });
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        }

    });

}


function getDetailOfManifAndSeance(manifid, seanceid) {


    //var sData = JSON.stringify({ eventId: manifid, sessionId: seanceid });
    var etatSelected = GetEtatSelected()
    var tarifIdSelected = GetTarifIDSelected()

    var sData = JSON.stringify({ eventId: manifid, sessionId: seanceid, _lstEtatsName: etatSelected, _lstTarifs: tarifIdSelected });

    $.ajax({
        type: "POST",
        url: sAjaxSourceUrl + 'GetDetailOfManifAndSeance',
        data: sData,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        beforeSend: function () {
            //            var html = ' <img src="../assets/images/loader.gif" alt="loader" />';
            //            $('#loading').html(html + $('#lblCalculCashStatement').text());
            //$("#loadingmodal").easyModal(optionsLoading);
            $('#btnNext').attr('disabled', 'disabled');
        },
        success: function (response) {
            //alert(response.d);
            /*var htmlfooter = ' <button type="button" class="btn btn-default" data-dismiss="modal">' + ReadXmlTranslate('btn_close') + '</button>' +
                                        ' <button type="button" class="btn btn-primary btn-export" id="btnExcelExportDetail">' + ReadXmlTranslate('export_xls') + '</button>'

            $('#modalGeneric .modal-body').html(response.d);
           // $('#modalGeneric .modal-footer').html(htmlfooter);
*
            $('#modalGeneric').modal('show');*/
            //var test = "<div class='row '><button class='btn btn-primary' id='btnPdfExportDetail'><span data-trad='btn_download_statement_cash_detail'>Télécharger</span></button></div><div class='table-responsive'><table class='table'><thead><tr><td data-trad='nomprenom'>nomprenom</td><td data-trad='identid'>identid</td><td data-trad='commande_id'>commande_id</td><td data-trad='date_operation'>date_operation</td><td data-trad='type_operation'>type_operation</td><td data-trad='montant1'>montant1</td><td data-trad='externe'>externe</td><td data-trad='motif'>motif</td><td data-trad='codebarre'>codebarre</td><td data-trad='type_tarif_nom'>type_tarif_nom</td><td data-trad='categ_nom'>categ_nom</td><td data-trad='rang'>rang</td><td data-trad='siege'>siege</td><td data-trad='zone_nom'>zone_nom</td><td data-trad='etage_nom'>etage_nom</td><td data-trad='section_nom'>section_nom</td></tr></thead><tbody><tr><td>ASSIF david</td><td>465</td><td>459</td><td>11/07/2017 15:11:01</td><td>E</td><td>70,0000000000</td><td>                                        </td><td>3340000200                                        </td><td>                                        3340000200                                        </td><td>PLEIN TARIF TELEPHONE</td><td>1ERE CATEGORIE</td><td>G</td><td>1</td><td>ZONE UNIQUE</td><td>ORCHESTRE</td><td>ORCHESTRE</td></tr><tr><td>ASSIF david</td><td>465</td><td>459</td><td>11/07/2017 15:11:03</td><td>E</td><td>70,0000000000</td><td>                                        </td><td>9142000300                                        </td><td>                                        9142000300                                        </td><td>PLEIN TARIF TELEPHONE</td><td>1ERE CATEGORIE</td><td>G</td><td>3</td><td>ZONE UNIQUE</td><td>ORCHESTRE</td><td>ORCHESTRE</td></tr><tr><td>DRAGONE erika</td><td>259</td><td>251</td><td>09/06/2017 15:26:56</td><td>E</td><td>70,0000000000</td><td>                                        </td><td>8090003260                                        </td><td>                                        8090003260                                        </td><td>PLEIN TARIF TELEPHONE</td><td>1ERE CATEGORIE</td><td>G</td><td>13</td><td>ZONE UNIQUE</td><td>ORCHESTRE</td><td>ORCHESTRE</td></tr><tr><td>FELIX gaelle</td><td>428</td><td>415</td><td>03/07/2017 15:25:09</td><td>E</td><td>55,0000000000</td><td>                                        </td><td>6000304081                                        </td><td>                                        6000304081                                        </td><td>PLEIN TARIF TELEPHONE</td><td>2EME CATEGORIE</td><td>S</td><td>53</td><td>ZONE UNIQUE</td><td>1ER BALCON</td><td>1 er BALCON S11</td></tr><tr><td>FELIX gaelle</td><td>428</td><td>415</td><td>03/07/2017 15:25:11</td><td>E</td><td>55,0000000000</td><td>                                        </td><td>3380000105                                        </td><td>                                        3380000105                                        </td><td>PLEIN TARIF TELEPHONE</td><td>2EME CATEGORIE</td><td>S</td><td>55</td><td>ZONE UNIQUE</td><td>1ER BALCON</td><td>1 er BALCON S11</td></tr><tr><td>LOPEZ claude</td><td>401</td><td>390</td><td>26/06/2017 14:55:04</td><td>E</td><td>70,0000000000</td><td>                                        </td><td>3320000107                                        </td><td>                                        3320000107                                        </td><td>PLEIN TARIF TELEPHONE</td><td>1ERE CATEGORIE</td><td>G</td><td>9</td><td>ZONE UNIQUE</td><td>ORCHESTRE</td><td>ORCHESTRE</td></tr><tr><td>LOPEZ claude</td><td>401</td><td>390</td><td>26/06/2017 14:55:06</td><td>E</td><td>70,0000000000</td><td>                                        </td><td>7180002030                                        </td><td>                                        7180002030                                        </td><td>PLEIN TARIF TELEPHONE</td><td>1ERE CATEGORIE</td><td>G</td><td>11</td><td>ZONE UNIQUE</td><td>ORCHESTRE</td><td>ORCHESTRE</td></tr><tr><td>MIKE&amp;8217;21JJJJ MIKE&amp;8217;21</td><td>473</td><td>676</td><td>02/07/2020 16:00:04</td><td>E</td><td>55,0000000000</td><td>3390000404                              </td><td>                                                  </td><td>3390000404                                                                                </td><td>WEB PLEIN TARIF</td><td>2EME CATEGORIE</td><td>LO 8</td><td>2</td><td>ZONE UNIQUE</td><td>ORCHESTRE</td><td>LOGES S18</td></tr><tr><td>MIKE&amp;8217;21JJJJ MIKE&amp;8217;21</td><td>473</td><td>676</td><td>02/07/2020 16:00:04</td><td>E</td><td>55,0000000000</td><td>6000305094                              </td><td>                                                  </td><td>6000305094                                                                                </td><td>WEB PLEIN TARIF</td><td>2EME CATEGORIE</td><td>LO 8</td><td>1</td><td>ZONE UNIQUE</td><td>ORCHESTRE</td><td>LOGES S18</td></tr><tr><td>MIKE&amp;8217;21JJJJ MIKE&amp;8217;21</td><td>473</td><td>675</td><td>02/07/2020 15:43:06</td><td>E</td><td>70,0000000000</td><td>8090004320                              </td><td>                                                  </td><td>8090004320                                                                                </td><td>WEB PLEIN TARIF</td><td>1ERE CATEGORIE</td><td>M</td><td>10</td><td>ZONE UNIQUE</td><td>ORCHESTRE</td><td>ORCHESTRE</td></tr><tr><td>MIKE&amp;8217;21JJJJ MIKE&amp;8217;21</td><td>473</td><td>675</td><td>02/07/2020 15:43:06</td><td>E</td><td>70,0000000000</td><td>2003030094                              </td><td>                                                  </td><td>2003030094                                                                                </td><td>WEB PLEIN TARIF</td><td>1ERE CATEGORIE</td><td>M</td><td>8</td><td>ZONE UNIQUE</td><td>ORCHESTRE</td><td>ORCHESTRE</td></tr><tr><td>MUDES marie helene</td><td>407</td><td>396</td><td>26/06/2017 15:15:31</td><td>E</td><td>55,0000000000</td><td>                                        </td><td>3340000104                                        </td><td>                                        3340000104                                        </td><td>PLEIN TARIF TELEPHONE</td><td>2EME CATEGORIE</td><td>S</td><td>57</td><td>ZONE UNIQUE</td><td>1ER BALCON</td><td>1 er BALCON S11</td></tr><tr><td>PULEGA nadia</td><td>324</td><td>316</td><td>14/06/2017 15:22:48</td><td>E</td><td>22,0000000000</td><td>                                        </td><td>8090006200                                        </td><td>                                        8090006200                                        </td><td>PLEIN TARIF TELEPHONE</td><td>4EME CATEGORIE</td><td>X</td><td>61</td><td>ZONE UNIQUE</td><td>2EME BALCON</td><td>2 ème BALCON S2</td></tr><tr><td>RECALDE jean pierre</td><td>417</td><td>404</td><td>29/06/2017 15:53:31</td><td>E</td><td>70,0000000000</td><td>                                        </td><td>3360000101                                        </td><td>                                        3360000101                                        </td><td>PLEIN TARIF TELEPHONE</td><td>1ERE CATEGORIE</td><td>G</td><td>5</td><td>ZONE UNIQUE</td><td>ORCHESTRE</td><td>ORCHESTRE</td></tr><tr><td>RECALDE jean pierre</td><td>417</td><td>404</td><td>29/06/2017 15:53:33</td><td>E</td><td>70,0000000000</td><td>                                        </td><td>7120006030                                        </td><td>                                        7120006030                                        </td><td>PLEIN TARIF TELEPHONE</td><td>1ERE CATEGORIE</td><td>G</td><td>7</td><td>ZONE UNIQUE</td><td>ORCHESTRE</td><td>ORCHESTRE</td></tr><tr><td>Vergnon Sébastien</td><td>493</td><td>718</td><td>30/11/2020 09:27:26</td><td>E</td><td>70,0000000000</td><td>6000302045                              </td><td>                                                  </td><td>6000302045                                                                                </td><td>WEB PLEIN TARIF</td><td>1ERE CATEGORIE</td><td>M</td><td>6</td><td>ZONE UNIQUE</td><td>ORCHESTRE</td><td>ORCHESTRE</td></tr><tr><td>Vergnon Sébastien</td><td>493</td><td>718</td><td>30/11/2020 09:27:26</td><td>E</td><td>70,0000000000</td><td>5500400033                              </td><td>                                                  </td><td>5500400033                                                                                </td><td>WEB PLEIN TARIF</td><td>1ERE CATEGORIE</td><td>M</td><td>4</td><td>ZONE UNIQUE</td><td>ORCHESTRE</td><td>ORCHESTRE</td></tr></tbody></table></div>";

            //Tableau afficher en popup dans une varible pour pouvoir la passer en paramètre pour l'export excel
            var tablesRetour = response.d;
            ShowGenericModal('&nbsp;', response.d, '', 'xl', '', true, true)

            initSelectHeaderTab();

            var selectExportDetailFormatTab = [];
            $('#selectExportDetailFormat').on('change', function () {
                selectExportDetailFormatTab = $('#selectExportDetailFormat').val()
            });



            $('#btnPdfExportDetail').on('click', function (e) {
                e.preventDefault();

                var footerhtml = '<button type="button" class="btn btn-default" data-dismiss="modal" data-trad="annuler">Annuler</button>' +
                    '<button type="button" class="btn btn-primary" id="confirmDownloadDetail">Oui, je veux télécharger</button>'

                ShowGenericModal("RGPD", 'RGPD text', footerhtml, '', '', true, true)


                //confirmer la réinitialisation
                $('#confirmDownloadDetail').off('click').click(function () {

                    var sData = JSON.stringify({ tables: tablesRetour, selectedCols: selectExportDetailFormatTab });

                    $.ajax({
                        type: "POST",
                        url: sAjaxSourceUrl + 'DownloadDetailExcelFile',
                        data: sData,
                        contentType: 'application/json; charset=utf-8',
                        dataType: "json",
                        success: function (response) {
                            // window.open(window.location.origin + response.d, response.d, 'width=1024,height=768,toolbar=no,location=no,directories=no,status=no,menubar=no,copyhistory=no');
                            // var urlpage = getAbsolutePath().substring(0, getAbsolutePath().lastIndexOf('/') - 5);

                            $('#loading').html('');
                            //window.open(urlpage + '/excels/' + response.d);

                            window.open(window.location.origin + response.d, response.d, 'width=1024,height=768,toolbar=no,location=no,directories=no,status=no,menubar=no,copyhistory=no');

                        },
                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                            ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
                            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
                        },
                        complete: function () {
                            $('#modalGeneric').modal('hide');
                        }
                    });


                });


            });
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        }


    });

}


function initSelectHeaderTab() {

    $('#modalGeneric').on('shown.bs.modal', function (e) {
        $('.chosen-select').chosen();

    })


}


jQuery(function ($) {
    $('[data-rel=tooltip]').tooltip();
    $('#fuelux-wizard').ace_wizard().on('change', function (e, info) {
        if (info.step == 1 && info.direction == "next" && $('input[name=chkSession]:checked').length > 0) {
            GetTarifsManifs();
        } else if (info.step == 2 && info.direction == "next" && $('input[type=checkbox]:checked').length > 0) {
            GetTarifIDSelected();
        } else if (info.step == 3 && info.direction == "next") {

            $('.dataTables').html('');
            GetSessionState();

            //Active les bouton exports
            $(".btn-export").prop("disabled", false);
        } else {
            if (info.direction == "previous" && info.step == 4) {
                if ($('#btnNext').hasAttr('disabled') == true)
                    $('#btnNext').attr('disabled', false);

                $('.dataTables').html('');
            }

            if (info.direction == "next") {
                // ShowError("error", $('#lblErrorSelectManifs').text(), "alert alert-danger alert-dismissable");
                ShowError("error", ReadXmlTranslate('msg_error_select_tarif'), "alert alert-danger alert-dismissable", 3000);
                return false;
            } else {
                $("#resume").hide();
                return true;
            }
        }

    }).on('finished', function (e) {
        //(btnPdfExport
        //  ShowError("error", "Merci", "success");
    }).on('stepclick', function (e) {
        return false; //prevent clicking on steps
    });
})



//supprime les accents sur le texte
String.prototype.sansAccent = function () {
    var accent = [
        /[\300-\306]/g, /[\340-\346]/g, // A, a
        /[\310-\313]/g, /[\350-\353]/g, // E, e
        /[\314-\317]/g, /[\354-\357]/g, // I, i
        /[\322-\330]/g, /[\362-\370]/g, // O, o
        /[\331-\334]/g, /[\371-\374]/g, // U, u
        /[\321]/g, /[\361]/g, // N, n
        /[\307]/g, /[\347]/g, // C, c
    ];
    var noaccent = ['A', 'a', 'E', 'e', 'I', 'i', 'O', 'o', 'U', 'u', 'N', 'n', 'C', 'c'];

    var str = this;
    for (var i = 0; i < accent.length; i++) {
        str = str.replace(accent[i], noaccent[i]);
    }

    return str;
}
