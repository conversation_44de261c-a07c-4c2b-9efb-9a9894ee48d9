﻿var _baseUrl = "/pages/Resources/translate.";
var _baseUrlDefault = "/pages/Resources/Default/translate.";
var arrTermsTranslateDefaultFr = new Array();
var arrTermsTranslateLoaded = new Array();

var sAjaxSourceUrl = "/basepage.aspx/";

var _url = "";

function fileExists(url) {
    if (url) {
        var req = new XMLHttpRequest();
        req.open('GET', url, false);
        req.send();
        return req.status == 200;
    } else {
        return false;
    }
}

function LoadTranslateInArray(isDefault, getXml, lang) {

    var language;
    if (lang === undefined)
        lang = "fr";

    if (IsIE() || IsSafari() || IsFirefox())
        language = navigator.language.split('-')[0];
    else
        language = window.navigator.language;

    if (getXml === undefined) {
        getXml = false;
    }

    if (!isDefault && !getXml)
        lang = language;

    var sData = JSON.stringify({
        langCode: lang,
        isDefault: isDefault,
        getXml: getXml
    });

    $.ajax({
        type: "POST",
        url: "/basepage.aspx/GetXml",
        async: false,
        contentType: "application/json; charset=utf-8",
        data: sData,
        success: function (xml) {
            if (xml.d != "") {
                if (getXml) {
                    resultXml = xml.d;
                }

                var arrayXml = JSON.parse(xml.d);

                $.each(arrayXml, function (i, item) {
                    var obj = {};
                    obj[item.Trad] = item.Value;

                    if (isDefault) {
                        arrTermsTranslateDefaultFr.push(obj);
                    } else {
                        arrTermsTranslateLoaded.push(obj);
                    }
                });

                console.log("📁 Fichier XML chargé:", _url);
                console.log("📊 Nombre d'éléments:", arrayXml.length);
                if (arrayXml.length > 0) {
                    console.log("🔍 Premier élément:", arrayXml[0]);
                }
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alert(textStatus);
        },
        complete: function () {
            // waitingDialog.hide();
        }
    });

    if (getXml) {
        return JSON.parse(resultXml);
    }
}

function LoadTranslateInArrayOld(isDefault, getXml, lang) {

    if (lang === undefined) {
        lang = "fr";
    }

    if (getXml === undefined) {
        getXml = false;
    }

    var sData = JSON.stringify({
        langCode: lang,
        isDefault: isDefault,
        getXml: getXml
    });

    var resultXml;
    $.ajax({
        type: "POST",
        url: "/basepage.aspx/GetXml",
        async: false,
        contentType: "application/json; charset=utf-8",
        data: sData,
        success: function (xml) {
            if (xml.d != "") {
                if (getXml) {
                    resultXml = xml.d;
                }

                var arrayXml = JSON.parse(xml.d);
                $.each(arrayXml, function (i, item) {
                    var obj = {};
                    obj[item.Trad] = item.Value;

                    if (isDefault) {
                        arrTermsTranslateDefaultFr.push(obj);
                    } else {
                        arrTermsTranslateLoaded.push(obj);
                    }
                });
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alert(textStatus);
        },
        complete: function () {
            // waitingDialog.hide();
        }
    });

    if (getXml) {
        return JSON.parse(resultXml);
    }
}

var isLoaded = false;

function ReadXmlTranslate(searchTerm) {
    var result = "";

    if (!isLoaded) {
        LoadTranslateInArray(false, false);
        LoadTranslateInArray(true, false);
        isLoaded = true;
        console.log("🔄 Chargement des traductions terminé");
        console.log("📊 Traductions chargées:", arrTermsTranslateLoaded.length);
        console.log("📊 Traductions par défaut:", arrTermsTranslateDefaultFr.length);
    } else {
        if (arrTermsTranslateLoaded.length > 0) {
            result = $.map(arrTermsTranslateLoaded, function (val) {
                return val[searchTerm];
            });
        }

        if (result.length == 0) {
            if (arrTermsTranslateDefaultFr.length > 0) {
                result = $.map(arrTermsTranslateDefaultFr, function (val) {
                    return val[searchTerm];
                });
            }
        }

        if (result.length == 0)
            console.log("le term trad " + searchTerm + " n'existe pas dans le tableau arrTermsTranslate - fichier chargé : ");
    }

    return result[0];
}

function LaunchTraduction() {
    if (!isLoaded) {
        LoadTranslateInArray(false);
        LoadTranslateInArray(true);
        console.log("load Translate in array");
    }

    $.each($('*[data-trad]'), function (indx, item) {
        if ($(this).is("input"))
            $(item).val(ReadXmlTranslate($(item).data('trad')));
        else
            $(item).html(ReadXmlTranslate($(item).data('trad')));
    });
}

function LoadFileTranslate(isDefault, lang) {
    var language = "fr";
    if (lang != undefined)
        language = lang;

    if (isDefault == false && lang == undefined) {
        language = window.navigator.userLanguage || window.navigator.language;

        if (IsIE() || IsSafari() || IsFirefox())
            language = navigator.language.split('-')[0];
        else
            language = window.navigator.language;
    }

    var _url = "";
    var _urlLang = _baseUrl + language + ".xml";
    var _urlLangDefault = _baseUrlDefault + language + ".xml";
    var _urlDefault = _baseUrlDefault + ".xml";

    if (fileExists(_urlLang)) {
        _url = _urlLang;
    } else if (fileExists(_urlLangDefault)) {
        _url = _urlLangDefault;
    } else if (fileExists(_urlDefault)) {
        _url = _urlDefault;
    } else {
        console.log("gros pb aucun fichier de langue");
    }

    console.log("url translate : " + _url);
    return _url;
}

function IsIE(userAgent) {
    userAgent = userAgent || navigator.userAgent;
    return userAgent.indexOf("MSIE ") > -1 || userAgent.indexOf("Trident/") > -1;
}

function IsSafari() {
    var is_safari = navigator.userAgent.toLowerCase().indexOf('safari/') > -1;
    return is_safari;
}

function IsFirefox() {
    var is_firefox = navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
    return is_firefox;
}

function GetNavigatorLanguage() {
    var language = window.navigator.userLanguage || window.navigator.language;

    if ($.browser.msie) {
        language = window.navigator.userLanguage.split('-')[0];
    }

    if ($.browser.safari) {
        language = window.navigator.language.split('-')[0];
    }

    return language;
}
