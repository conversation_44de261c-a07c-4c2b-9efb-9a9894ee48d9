﻿var _baseUrl = "/pages/Resources/translate.";
var _baseUrlDefault = "/pages/Resources/Default/translate.";
var arrTermsTranslateDefaultFr = new Array();
var arrTermsTranslateLoaded = new Array();

var sAjaxSourceUrl = "/basepage.aspx/";

var _url = "";

function fileExists(url) {
    if (url) {
        var req = new XMLHttpRequest();
        req.open('GET', url, false);
        req.send();
        return req.status == 200;
    } else {
        return false;
    }
}

function LoadTranslateInArray(isDefault, getXml, lang) {

    var language;
    if (lang === undefined) {
        // Si aucune langue n'est spécifiée, utiliser la langue du navigateur
        if (IsIE() || IsSafari() || IsFirefox())
            language = navigator.language.split('-')[0];
        else
            language = window.navigator.language;

        lang = language || "fr"; // Fallback sur français si pas de langue détectée
    }

    if (getXml === undefined) {
        getXml = false;
    }

    // Ne plus écraser le paramètre lang - respecter la langue demandée
    console.log("🌍 LoadTranslateInArray - Langue demandée:", lang, "isDefault:", isDefault);

    var sData = JSON.stringify({
        langCode: lang,
        isDefault: isDefault,
        getXml: getXml
    });

    $.ajax({
        type: "POST",
        url: "/basepage.aspx/GetXml",
        async: false,
        contentType: "application/json; charset=utf-8",
        data: sData,
        success: function (xml) {
            if (xml.d != "") {
                if (getXml) {
                    resultXml = xml.d;
                }

                var arrayXml = JSON.parse(xml.d);

                $.each(arrayXml, function (i, item) {
                    var obj = {};
                    obj[item.Trad] = item.Value;

                    if (isDefault) {
                        arrTermsTranslateDefaultFr.push(obj);
                    } else {
                        arrTermsTranslateLoaded.push(obj);
                    }
                });

                console.log("📁 Fichier XML chargé:", _url);
                console.log("📊 Nombre d'éléments:", arrayXml.length);
                if (arrayXml.length > 0) {
                    console.log("🔍 Premier élément:", arrayXml[0]);
                }
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alert(textStatus);
        },
        complete: function () {
            // waitingDialog.hide();
        }
    });

    if (getXml) {
        return JSON.parse(resultXml);
    }
}

function LoadTranslateInArrayOld(isDefault, getXml, lang) {

    if (lang === undefined) {
        lang = "fr";
    }

    if (getXml === undefined) {
        getXml = false;
    }

    var sData = JSON.stringify({
        langCode: lang,
        isDefault: isDefault,
        getXml: getXml
    });

    var resultXml;
    $.ajax({
        type: "POST",
        url: "/basepage.aspx/GetXml",
        async: false,
        contentType: "application/json; charset=utf-8",
        data: sData,
        success: function (xml) {
            if (xml.d != "") {
                if (getXml) {
                    resultXml = xml.d;
                }

                var arrayXml = JSON.parse(xml.d);
                $.each(arrayXml, function (i, item) {
                    var obj = {};
                    obj[item.Trad] = item.Value;

                    if (isDefault) {
                        arrTermsTranslateDefaultFr.push(obj);
                    } else {
                        arrTermsTranslateLoaded.push(obj);
                    }
                });
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alert(textStatus);
        },
        complete: function () {
            // waitingDialog.hide();
        }
    });

    if (getXml) {
        return JSON.parse(resultXml);
    }
}

var isLoaded = false;
var currentLanguage = "fr"; // Langue par défaut

// Fonction pour récupérer la langue sélectionnée
function getSelectedLanguage() {
    // 1. Vérifier le paramètre URL 'lang'
    var urlParams = new URLSearchParams(window.location.search);
    var langFromUrl = urlParams.get('lang');
    if (langFromUrl && langFromUrl !== "") {
        console.log("🌍 Langue depuis URL:", langFromUrl);
        return langFromUrl;
    }

    // 2. Essayer de récupérer la langue depuis différentes sources
    if (typeof ddlLang !== 'undefined' && ddlLang !== "") {
        console.log("🌍 Langue depuis ddlLang:", ddlLang);
        return ddlLang;
    }
    if ($("#selectLang").length > 0 && $("#selectLang").val() !== "") {
        console.log("🌍 Langue depuis #selectLang:", $("#selectLang").val());
        return $("#selectLang").val();
    }
    if ($("#selectLangues").length > 0 && $("#selectLangues").val() !== "") {
        console.log("🌍 Langue depuis #selectLangues:", $("#selectLangues").val());
        return $("#selectLangues").val();
    }

    // 3. Si currentLanguage a été forcée, l'utiliser
    if (typeof currentLanguage !== 'undefined' && currentLanguage !== 'fr') {
        console.log("🌍 Langue depuis currentLanguage:", currentLanguage);
        return currentLanguage;
    }

    // 4. Par défaut, utiliser français
    console.log("🌍 Langue par défaut: fr");
    return 'fr';
}

function ReadXmlTranslate(searchTerm) {
    console.log("=== ReadXmlTranslate appelée avec: " + searchTerm + " ===");

    var result = "";

    // Vérifier si la langue a changé ou si ce n'est pas encore chargé
    var selectedLang = getSelectedLanguage();
    if (!isLoaded || currentLanguage !== selectedLang) {
        console.log("Chargement des traductions pour la langue: " + selectedLang);
        currentLanguage = selectedLang;

        // Réinitialiser les tableaux
        arrTermsTranslateLoaded = [];
        arrTermsTranslateDefaultFr = [];

        LoadTranslateInArray(false, false, selectedLang);
        LoadTranslateInArray(true, false, selectedLang);
        isLoaded = true;
        console.log("loaded Translate in array for language: " + selectedLang);
    }

    // Recherche dans les traductions chargées (toujours faire la recherche)
    if (arrTermsTranslateLoaded.length > 0) {
        result = $.map(arrTermsTranslateLoaded, function (val) {
            return val[searchTerm];
        });
    }

    if (result.length == 0) {
        if (arrTermsTranslateDefaultFr.length > 0) {
            result = $.map(arrTermsTranslateDefaultFr, function (val) {
                return val[searchTerm];
            });
        }
    }

    if (result.length == 0) {
        console.warn("Traduction manquante pour: " + searchTerm); // Message plus discret
        return searchTerm; // Retourne la clé si pas de traduction trouvée
    }

    return result[0] || searchTerm; // Sécurité supplémentaire
}

// Fonction pour forcer le rechargement des traductions
function ReloadTranslations(newLanguage) {
    console.log("=== ReloadTranslations appelée pour la langue: " + newLanguage + " ===");

    // Forcer le rechargement
    isLoaded = false;
    currentLanguage = newLanguage;

    // Réinitialiser les tableaux
    arrTermsTranslateLoaded = [];
    arrTermsTranslateDefaultFr = [];

    // Recharger avec la nouvelle langue
    LoadTranslateInArray(false, false, newLanguage);
    LoadTranslateInArray(true, false, newLanguage);
    isLoaded = true;

    console.log("Traductions rechargées pour la langue: " + newLanguage);

    // Relancer la traduction de tous les éléments
    LaunchTraduction();
}

function LaunchTraduction() {
    if (!isLoaded) {
        LoadTranslateInArray(false);
        LoadTranslateInArray(true);
        console.log("load Translate in array");
    }

    $.each($('*[data-trad]'), function (indx, item) {
        if ($(this).is("input"))
            $(item).val(ReadXmlTranslate($(item).data('trad')));
        else
            $(item).html(ReadXmlTranslate($(item).data('trad')));
    });
}

function LoadFileTranslate(isDefault, lang) {
    var language = "fr";
    if (lang != undefined)
        language = lang;

    if (isDefault == false && lang == undefined) {
        language = window.navigator.userLanguage || window.navigator.language;

        if (IsIE() || IsSafari() || IsFirefox())
            language = navigator.language.split('-')[0];
        else
            language = window.navigator.language;
    }

    var _url = "";
    var _urlLang = _baseUrl + language + ".xml";
    var _urlLangDefault = _baseUrlDefault + language + ".xml";
    var _urlDefault = _baseUrlDefault + ".xml";

    if (fileExists(_urlLang)) {
        _url = _urlLang;
    } else if (fileExists(_urlLangDefault)) {
        _url = _urlLangDefault;
    } else if (fileExists(_urlDefault)) {
        _url = _urlDefault;
    } else {
        console.log("gros pb aucun fichier de langue");
    }

    console.log("url translate : " + _url);
    return _url;
}

function IsIE(userAgent) {
    userAgent = userAgent || navigator.userAgent;
    return userAgent.indexOf("MSIE ") > -1 || userAgent.indexOf("Trident/") > -1;
}

function IsSafari() {
    var is_safari = navigator.userAgent.toLowerCase().indexOf('safari/') > -1;
    return is_safari;
}

function IsFirefox() {
    var is_firefox = navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
    return is_firefox;
}

function GetNavigatorLanguage() {
    var language = window.navigator.userLanguage || window.navigator.language;

    if ($.browser.msie) {
        language = window.navigator.userLanguage.split('-')[0];
    }

    if ($.browser.safari) {
        language = window.navigator.language.split('-')[0];
    }

    return language;
}

// Fonction d'initialisation pour surveiller les changements de langue
function InitLanguageWatcher() {
    console.log("=== InitLanguageWatcher ===");

    // Surveiller les changements sur les sélecteurs de langue
    $(document).on('change', '#selectLang, #selectLangues', function() {
        var newLang = $(this).val();
        console.log("Changement de langue détecté: " + newLang);

        // Mettre à jour ddlLang si elle existe
        if (typeof ddlLang !== 'undefined') {
            ddlLang = newLang;
        }

        if (newLang && newLang !== currentLanguage) {
            ReloadTranslations(newLang);
        }
    });

    // Surveiller les clics sur les boutons de validation de langue
    $(document).on('click', '#btnLanguagesValider', function() {
        console.log("Bouton de validation de langue cliqué");
        setTimeout(function() {
            var newLang = getSelectedLanguage();

            // Mettre à jour ddlLang si elle existe
            if (typeof ddlLang !== 'undefined') {
                ddlLang = newLang;
            }

            if (newLang && newLang !== currentLanguage) {
                ReloadTranslations(newLang);
            }
        }, 100); // Petit délai pour laisser le temps aux autres scripts de s'exécuter
    });
}

// Initialiser le surveillant de langue quand le document est prêt
$(document).ready(function() {
    InitLanguageWatcher();

    // Vérifier immédiatement si la langue dans l'URL est différente de la langue par défaut
    setTimeout(function() {
        var selectedLang = getSelectedLanguage();
        console.log("🚀 Langue détectée au démarrage:", selectedLang);

        if (selectedLang !== 'fr' && selectedLang !== currentLanguage) {
            console.log("🔄 Rechargement des traductions pour:", selectedLang);
            ReloadTranslations(selectedLang);
        }
    }, 500); // Petit délai pour laisser le temps aux autres scripts de s'initialiser
});
