﻿using login.classes;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading;
using System.Web;
using System.Web.Script.Services;
using System.Web.Services;
using System.Xml.Linq;

namespace login
{
    public partial class basePage : System.Web.UI.Page
    {
        private static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public int structureId;
        public int adminId;
        public List<int> listGroupe;
        protected NameValueCollection QueryStrings;

        protected void Page_PreInit(object sender, EventArgs e)
        {

            MyConfigurationManager.SetUserLanguage();


            if (Session["adminId"] == null
                || !int.TryParse(Session["adminId"].ToString(), out adminId)
                || Session["idstructure"] == null
                || !int.TryParse(Session["idstructure"].ToString(), out structureId))
            {
                Response.Redirect("~/Login.aspx", true);
            }



            /*listGroupe = new List<int>();


            System.Web.HttpContext.Current.Session["mygroupeDeManifs"] = "all";

            if (System.Web.HttpContext.Current.Session["mygroupeDeManifs"] != null && System.Web.HttpContext.Current.Session["mygroupeDeManifs"].ToString() == "all")
            {

                wsThemisAdmin2010.WSAdminClient wsAdmin = new wsThemisAdmin2010.WSAdminClient();
                try
                {

                    listGroupe = wsAdmin.GetListGroupeDeManifs(adminId).ToList();

                    if (listGroupe.Count > 0)
                    {
                        foreach (var item in listGroupe)
                        {

                            var last = listGroupe.Last();

                            if (item == last)
                            {
                                System.Web.HttpContext.Current.Session["mygroupeDeManifs"] = item;

                            }
                            else
                            {
                                System.Web.HttpContext.Current.Session["mygroupeDeManifs"] = item + ",";

                            }

                        }

                    }


                    if (System.Web.HttpContext.Current.Session["mygroupeDeManifs"] != null
                        && System.Web.HttpContext.Current.Session["mygroupeDeManifs"].ToString() != "all")
                    {

                        listGroupe.Add(int.Parse(System.Web.HttpContext.Current.Session["mygroupeDeManifs"].ToString()));
                    }

                }
                catch (Exception ex)
                {
                    //                  log.Error("catch - LoadStructuresList : structure id est null :" + (string)HttpContext.Current.Request.Cookies["StructureId"].Value);
                    log.Error("catch - Page_PreInit : " + ex.Message);
                    throw new Exception("catch - Page_PreInit : " + ex.Message);
                }
                finally
                {
                    if (wsAdmin.State == System.ServiceModel.CommunicationState.Opened)
                        wsAdmin.Close();
                }
            }*/

        }

        protected string GetFiltre()
        {
            //ajout des filtres
            string strFiltreLieuID = "", strFiltreGroupeID = "", strFiltreDateDeb = "", strFiltreDateFin = "", strFiltreNomManif = "", strID = "", strIndex = "";
            if (GetRequest("FiltreLieuID") != null && GetRequest("FiltreLieuID") != "")
            {
                strFiltreLieuID = "&FiltreLieuID=" + GetRequest("FiltreLieuID");
            }

            if (GetRequest("FiltreGroupeID") != null && GetRequest("FiltreGroupeID") != "")
            {
                strFiltreGroupeID = "&FiltreGroupeID=" + GetRequest("FiltreGroupeID");
            }

            if (GetRequest("FiltreDateDeb") != null && GetRequest("FiltreDateDeb") != "")
            {
                strFiltreDateDeb = "&FiltreDateDeb=" + GetRequest("FiltreDateDeb");
            }

            if (GetRequest("FiltreDateFin") != null && GetRequest("FiltreDateFin") != "")
            {
                strFiltreDateFin = "&FiltreDateFin=" + GetRequest("FiltreDateFin");
            }

            if (GetRequest("FiltreNomManif") != null && GetRequest("FiltreNomManif") != "")
            {
                strFiltreNomManif = "&FiltreNomManif=" + GetRequest("FiltreNomManif");
            }

            if (GetRequest("gvMaitreSelectedIndex") != null && GetRequest("gvMaitreSelectedIndex") != "")
            {
                strIndex = "&gvMaitreSelectedIndex=" + GetRequest("gvMaitreSelectedIndex");
            }

            // public 0 / 1
            string strPublic = "&Public=" + GetRequest("Public");

            string strOffre = "";
            if (GetRequest("OffreId") != null)
            {
                strOffre = "&OffreId=" + GetRequest("OffreId");
            }

            return strFiltreLieuID + strFiltreGroupeID + strFiltreDateDeb + strFiltreDateFin + strID + strIndex + strFiltreNomManif + strPublic + strOffre;

        }
        public string GetRequest(string keyInrequest)
        {
            if (Request[keyInrequest] != null)
            {
                return Request[keyInrequest].ToString();
            }
            else
            {
                if (Request.QueryString[keyInrequest] != null)
                {
                    return Request.QueryString[keyInrequest].ToString();
                }
                else
                {
                    return null;
                }
            }
        }

        public void GetLanguage()
        {
            #region Lecture de langue par défaut

            if (Session["idstructure"] == null)
            {
                if (Request.QueryString["idstructure"] != "" && Request.QueryString["idstructure"] != null && int.TryParse(Request.QueryString["idstructure"], out int resul))
                {
                    Session["idstructure"] = Request.QueryString["idstructure"];
                }
                else
                {
                    // TODO gerer erreur
                    //  Session["ErrorMsg"] = Resources.Resource.String11;// "Votre Session est perdue";
                    // Server.Transfer("ErrorPageN.aspx", false);
                }
            }
            if (Session["SVarLangue"] == null)
            {
                //recherche cookies..???
                //if (mySSC["PARAMLANGUE"] != null && mySSC["PARAMLANGUE"] != "")
                //{
                //    Session["SVarLangue"] = mySSC["PARAMLANGUE"];
                //}

                //else 
                //Session["SVarLangue"] = "fr";
            }
            if (Session["SVarLangue"] != null && (string)Session["SVarLangue"] != "")
            {
                string strLangue = (string)Session["SVarLangue"];
                System.Threading.Thread.CurrentThread.CurrentUICulture = new System.Globalization.CultureInfo(strLangue);
                System.Threading.Thread.CurrentThread.CurrentCulture = System.Globalization.CultureInfo.CreateSpecificCulture(strLangue);
            }
            #endregion
        }


        //protected void Page_Load(object sender, EventArgs e)
        //{

        //}
        protected void Page_Init(object sender, EventArgs e)
        {

            MyConfigurationManager.SetUserLanguage();
        }



        /// <summary>
        /// récupère et fusionne les fichiers de traduction defaut et structure pour la plateforme (indiv, abo, ...)
        /// </summary>
        public static IEnumerable<XElement> GetTranslateXml(string structureId, string xmlKey, string plateform, bool isDefaultFile, string langue)
        {

            string physiqueXmlPath = MyConfigurationManager.AppSettings(xmlKey);
            physiqueXmlPath = physiqueXmlPath.Replace("[plateforme]", plateform);
            physiqueXmlPath = physiqueXmlPath.Replace("[.lang]", "." + langue);

            if ((isDefaultFile))
            {
                physiqueXmlPath = physiqueXmlPath.Replace("[idstructure]", "DEFAULT");
            }
            else
            {
                physiqueXmlPath = physiqueXmlPath.Replace("[idstructure]", structureId);
            }

            if (File.Exists(physiqueXmlPath))
            {
                XDocument xml1 = XDocument.Load(physiqueXmlPath);

                return xml1.Descendants("root");
            }
            return new List<XElement>();
        }


        /// <summary>
        /// récupère et fusionne les fichiers de traduction defaut et structure pour la plateforme (indiv, abo, ...)
        /// </summary>
        public static IEnumerable<XElement> GetTranslateXmlOld(string structureId, string plateform)
        {

            string xmlFileDefault = @"\\172.30.200.109\customerfiles\TEST\Default\Indiv2\RESOURCES\translate.fr.xml";
            string xmlFileStructure = @"\\172.30.200.109\customerfiles\TEST\0218\Indiv2\RESOURCES\translate.fr.xml";


            if (File.Exists(xmlFileDefault) && File.Exists(xmlFileStructure))
            {
                XDocument xml1 = XDocument.Load(xmlFileDefault);
                XDocument xml2 = XDocument.Load(xmlFileStructure);


                IEnumerable<XElement> corpsDoc1 = xml1.Root.Elements();
                IEnumerable<XElement> corpsDoc2 = xml2.Root.Elements();

                IEnumerable<XElement> merge = corpsDoc1.Concat(corpsDoc2);


                //Combine and remove duplicates
                IEnumerable<XElement> combinedUnique = xml1.Descendants("root")
                                          .Union(xml2.Descendants("root"));


                combinedUnique = xml1.Descendants()
                                       .Union(xml2.Descendants());

                XElement gg = combinedUnique.First();


                return combinedUnique;

            }
            else
            {
                XDocument xml1 = XDocument.Load(xmlFileDefault);
                IEnumerable<XElement> xmlDefault = xml1.Descendants("root");


                return xmlDefault;
            }


        }


        [WebMethod(EnableSession = true)]
        public static string GetXml(string langCode, bool isDefault, bool getXml)
        {
            MyConfigurationManager.SetUserLanguage();


            Thread.CurrentThread.CurrentCulture = CultureInfo.CreateSpecificCulture(HttpContext.Current.Request.UserLanguages[0]);
            string userLanguage = Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName;

            string resourcePath = MyConfigurationManager.AppSettings("ResourcesPath").ToString();

            XDocument xmlDoc = null;
            if (isDefault)
            {
                try
                {
                    string translatePath = resourcePath + $"translate.fr.xml";

                    if (File.Exists(translatePath))
                    {
                        xmlDoc = XDocument.Load(resourcePath + "translate.fr.xml");
                       
                    }
                    else
                    {
                        log.Error(" Fichier de traduction introuvable: " + translatePath);
                    }
                }
                catch (FileNotFoundException)
                {

                    throw;
                }

            }
            else
            {
                try
                {
                    string translatePath = resourcePath + $"translate.{langCode}.xml";

                    if (File.Exists(translatePath))
                    {
                        xmlDoc = XDocument.Load(resourcePath + $"translate.{langCode}.xml");
                       
                    }
                    else
                    {
                        log.Error("Fichier de traduction langue introuvable: " + translatePath);
                    }
                }
                catch (FileNotFoundException)
                {

                    throw;
                }


            }

            if (xmlDoc != null)
            {
                List<Translate> xmlKeysValues = xmlDoc.Descendants("Root").Descendants("Generals").Descendants()
                        .Select(item => new Translate { Trad = item.Attribute("trad").Value, Value = item.Value }).ToList();

           
                if (xmlKeysValues.Count > 0)
                {
                    log.Debug("🔍 Première traduction: " + xmlKeysValues[0].Trad + " = " + xmlKeysValues[0].Value);
                }

                log.Debug("Load file : " + resourcePath + "translate.fr.xml");
                //return xmlDoc.ToString();
                //return xmlKeysValues;

                string Json = JsonConvert.SerializeObject(xmlKeysValues, Newtonsoft.Json.Formatting.None);
           
                return Json;

            }
            else
            {
                log.Error(" xmlDoc est null - aucun fichier de traduction chargé");
            }



            return "";

        }
    }
}