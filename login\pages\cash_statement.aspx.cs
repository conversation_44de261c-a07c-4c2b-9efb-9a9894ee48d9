﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data.SqlClient;
using WebTracing2010;
using System.Web.Services;
using System.Threading;
using System.Globalization;
using System.Data;
using login.classes;
using Newtonsoft.Json;
using System.Text;
//using iTextSharp.text;
using System.Xml.Linq;
using System.IO;
using Excel = Microsoft.Office.Interop.Excel;
using System.Xml;
using System.Web.Script.Services;
using utilitaires2010;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
//using iTextSharp.text.html.simpleparser;
//using iTextSharp.text.pdf;
using TheArtOfDev.HtmlRenderer.PdfSharp;
using PdfSharp.Pdf;
using iTextSharp.text;
using TheArtOfDev.HtmlRenderer.Core.Entities;

namespace login.pages_stats
{

    public partial class cash_statement : basePage
    {
        private static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        protected void Page_Load(object sender, EventArgs e)
        {

        }


        //Obitent la liste des evenments  avec les séances
        [WebMethod(EnableSession = true)]
        public static string GetEventsData(bool isFutureSessions, string startEventsDate, string endEventsDate)
        {

            DateTime dStartEvendDate;
            DateTime dEndEventDate;

            if (  (!string.IsNullOrEmpty(startEventsDate) && !DateTime.TryParse(startEventsDate, out dStartEvendDate)) || (!string.IsNullOrEmpty(startEventsDate) && !DateTime.TryParse(endEventsDate, out dEndEventDate)))            {
                throw new ArgumentException("invalid Dates");
            }



            MyConfigurationManager.SetUserLanguage();
            string userLanguage = MyConfigurationManager.GetUserLanguage();
            //Thread.CurrentThread.CurrentCulture = CultureInfo.CreateSpecificCulture(HttpContext.Current.Request.UserLanguages[0]);
            //string userLanguage = Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName;

            List<login.wsThemisAdmin2010.EventEntity> lstEvent = new List<login.wsThemisAdmin2010.EventEntity>();

            if (System.Web.HttpContext.Current.Session["idstructure"] != null && HttpContext.Current.Session["adminId"] != null)
            {
                string sStructureID = (string)System.Web.HttpContext.Current.Session["idstructure"];

                wsThemisAdmin2010.WSAdminClient wsClt = new wsThemisAdmin2010.WSAdminClient();

                try
                {

                    List<int> listGroupe = new List<int>();

                    if (System.Web.HttpContext.Current.Session["mygroupeDeManifs"] != null && System.Web.HttpContext.Current.Session["mygroupeDeManifs"].ToString() != "all")
                    {
                        List<string> lstGrpManifs = System.Web.HttpContext.Current.Session["mygroupeDeManifs"].ToString().Split(',').ToList();

                        foreach (string item in lstGrpManifs)
                        {
                            listGroupe.Add(int.Parse(item));
                        }

                    }

                    double delaiCache = 900; // 15 mns

                    string cacheName = $"eventsgrouplist_{sStructureID}_{isFutureSessions}_{string.Join("_", listGroupe)}";
                    //Si aucun cache ou si les dates sont remplies alors on requête en DB
                    if (HttpContext.Current.Cache[cacheName] == null || (!string.IsNullOrEmpty(startEventsDate) && !string.IsNullOrEmpty(endEventsDate)))
                    {
                        log.DebugFormat("Appel du Webservice LoadListEventsSessions avec structure : {0} et langage : {1}", sStructureID, userLanguage);
                        lstEvent = wsClt.LoadListEventsSessions(sStructureID, userLanguage, listGroupe.ToArray(), startEventsDate, endEventsDate).ToList();

                        HttpContext.Current.Cache.Insert(cacheName, lstEvent, null, DateTime.Now.AddSeconds(delaiCache), TimeSpan.Zero);
                    }
                    else
                    {
                        lstEvent = ((List<login.wsThemisAdmin2010.EventEntity>)HttpContext.Current.Cache[cacheName]);
                    }


                    
                    //Si la check des séances futures est désactivée
                    if (!isFutureSessions)
                    {
                        var newEventsGroupList = new List<login.wsThemisAdmin2010.EventEntity>();
                        foreach (var evt in lstEvent)
                        {
                            var sessionsList = evt.ListSessions.Where(s => s.IsFutureSession == true).ToArray();
                            if (sessionsList.Length > 0)
                            {
                                evt.ListSessions = sessionsList;
                                newEventsGroupList.Add(evt);
                            }
                        }
                        lstEvent = newEventsGroupList;
                    }
                }
                catch (Exception ex)
                {
                    log.Error("catch LoadListEventsSessions du web service " + ex.Message);
                    throw new Exception(ex.Message);

                }
                finally
                {
                    if (wsClt.State == System.ServiceModel.CommunicationState.Opened)
                        wsClt.Close();
                }

            }

            System.Web.Script.Serialization.JavaScriptSerializer oSerializer = new System.Web.Script.Serialization.JavaScriptSerializer();
            oSerializer.MaxJsonLength = int.MaxValue;
            string sJSON = oSerializer.Serialize(lstEvent);

            return sJSON;

            //return lstEvent;
        }


        //Obtien la liste des tarif pour la ou les evenements sélectionnés
        [WebMethod(EnableSession = true)]
        public static string GetTarifsManifs(List<int> _sessionId)
        {
            MyConfigurationManager.SetUserLanguage();

            DataSet dsListTarifSessions = null;
            if (System.Web.HttpContext.Current.Session["idstructure"] != null && HttpContext.Current.Session["adminId"] != null)
            {
                string sStructureID = (string)System.Web.HttpContext.Current.Session["idstructure"];
                wsThemisAdmin2010.WSAdminClient wsClt = new wsThemisAdmin2010.WSAdminClient();

                try
                {
                    log.DebugFormat("Appel du Webservice GetListeSessions avec structure : {0} et sessionId : {1}", sStructureID, _sessionId);


                    string xmlListTarifSessions = wsClt.GetListeSessions(sStructureID, _sessionId.ToArray());

                    dsListTarifSessions = DBFunctions.ConvertXMLToDataSet(xmlListTarifSessions);
                }
                catch (Exception ex)
                {
                    log.Error("catch GetListeSessions du web service " + ex.Message);
                    throw new Exception(ex.Message);
                }
                finally
                {
                    if (wsClt.State == System.ServiceModel.CommunicationState.Opened)
                        wsClt.Close();
                }
            }

            DataTableCustom dtCustom = new DataTableCustom();
            string dtConvertCustom = dtCustom.ConvertDataTabletoString(dsListTarifSessions.Tables["MyDS"]);

            return dtConvertCustom;
        }

        [WebMethod(EnableSession = true)]
        public static string GetSessionState(List<int> sessionsId, List<int> tarifId, List<string> eventsName, string startDate, string endDate, List<string> _lstEtatsName)
        {
            MyConfigurationManager.SetUserLanguage();

            DataSet dsListTarifSessions = new DataSet();

            if (System.Web.HttpContext.Current.Session["idstructure"] != null && HttpContext.Current.Session["adminId"] != null)
            {
                string sStructureID = (string)System.Web.HttpContext.Current.Session["idstructure"];
                Thread.CurrentThread.CurrentCulture = CultureInfo.CreateSpecificCulture(HttpContext.Current.Request.UserLanguages[0]);
                string userLanguage = Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName;


                string sdtDebIso = "";
                string sdtFinIso = "";

                if (!string.IsNullOrEmpty(startDate) && !string.IsNullOrEmpty(endDate))
                {

                    // convertit les date début et fin
                    string strFromDate = DataTableCustom.FormatDateString(startDate);
                    string strToDate = DataTableCustom.FormatDateString(endDate);

                    DateTime dtFromDate = Convert.ToDateTime(strFromDate, DateTimeFormatInfo.InvariantInfo);
                    DateTime dtToDate = Convert.ToDateTime(strToDate, DateTimeFormatInfo.InvariantInfo);
                    sdtDebIso = dtFromDate.Year.ToString("0000") + dtFromDate.Month.ToString("00") + dtFromDate.Day.ToString("00") + " " + dtFromDate.Hour.ToString("00") + ":" + dtFromDate.Minute.ToString("00") + ":" + dtFromDate.Second.ToString("00");
                    sdtFinIso = dtToDate.Year.ToString("0000") + dtToDate.Month.ToString("00") + dtToDate.Day.ToString("00") + " " + dtToDate.Hour.ToString("00") + ":" + dtToDate.Minute.ToString("00") + ":" + dtToDate.Second.ToString("00");

                }

                //convertit la liste de string en string séparer avec des virgules
                // string strEtatsName = string.Join<string>(",", _lstEtatsName);

                wsThemisAdmin2010.WSAdminClient wsClt = new wsThemisAdmin2010.WSAdminClient();

                try
                {
                    log.DebugFormat("Appel du Webservice GetListeSessions avec structure : {0} ", sStructureID);
                    //                    listTarifSessions = wsClt.GetStatementCashList(sStructureID, sessionsId.ToArray(), tarifId.ToArray(), sdtDebIso, sdtFinIso, userLanguage, strEtatsName);


                    dsListTarifSessions = wsClt.GetStatementCashList(sStructureID, sessionsId.ToArray(), tarifId.ToArray(), sdtDebIso, sdtFinIso, userLanguage, _lstEtatsName.ToArray());

                }
                catch (Exception ex)
                {
                    log.Error("catch GetListeSessions du web service " + ex.Message);
                    throw new Exception(ex.Message);
                }
                finally
                {
                    if (wsClt.State == System.ServiceModel.CommunicationState.Opened)
                        wsClt.Close();
                }

            }

            StringBuilder sb = new StringBuilder();

            string dtConvertCustomHtml = "";
            List<string> lst = new List<string>();

            Dictionary<string, string> dict = new Dictionary<string, string>();
            bool isOK = false;
            bool isAlreadyNull = false;


            List<int> lstTotalSommEntreesFinal = new List<int>();
            List<decimal> lstTotalSommFinal = new List<decimal>();

            string htmltableauTotalFinal = "<div class='table-responsive'><table class='table table-bordered '>";
            htmltableauTotalFinal += "<thead><tr><td class='bgGrey' data-trad='nbr_total_entrees_final'></td><td class='bgGrey' data-trad='sum_totale_final'><!--traduction en js --></td></tr></thead>";
            htmltableauTotalFinal += "<tfoot></tfoot><tbody><tr><td>[totalSommEntreesFinal]</td><td  class='amount'>[totalSommFinal]</td></tr></tbody>";


            log.Info("listTarifSessions.Tables count : " + dsListTarifSessions.Tables.Count);
            foreach (DataTable item in dsListTarifSessions.Tables)
            {
                // dtConvertCustom = dtCustom.ConvertDataTabletoString(item);
                if (item.Rows.Count > 0)
                {
                    dtConvertCustomHtml += ConvertDataTableToHTML(item, ref lstTotalSommEntreesFinal, ref lstTotalSommFinal);

                    //sb.Append(dtConvertCustomHtml);
                    isOK = true;
                }
                else
                {
                    string nom_manif = "";
                    if (item.Rows.Count > 0)
                    {
                        nom_manif = item.Rows[0]["manifestation_nom"].ToString();
                    }
                    else
                    {
                        if (!isOK && !isAlreadyNull)
                        {
                            //dtConvertCustomHtml += GeneralTerms.ResultCashStatement + string.Join(", ", eventsName.Where(l => l != nom_manif));
                            //a modifier
                            dtConvertCustomHtml += string.Join(", ", eventsName.Where(l => l != nom_manif));
                            isAlreadyNull = true;
                        }
                    }
                }
            }

            // string dtConvertCustom = dtCustom.ConvertDataTabletoString(listTarifSessions.Tables["MyDS"]);

            if (dsListTarifSessions != null)
            {

                htmltableauTotalFinal = htmltableauTotalFinal.Replace("[totalSommEntreesFinal]", lstTotalSommEntreesFinal.Sum().ToString());
                htmltableauTotalFinal = htmltableauTotalFinal.Replace("[totalSommFinal]", lstTotalSommFinal.Sum().ToString("F"));


                sb.Append("OK:" + htmltableauTotalFinal + dtConvertCustomHtml);
            }
            else
            {
                log.Debug("ERROR: dtConvertCustomHtml est null :" + dtConvertCustomHtml);
                sb.Append("ERROR: dtConvertCustomHtml est null").ToString();
            }



            return sb.ToString();
            // return lst;


        }

        


        [WebMethod(EnableSession = true)]
        public static string GetFullDetail(int eventId, int sessionId, List<int> _lstTarifs)
        {
            MyConfigurationManager.SetUserLanguage();

            if (System.Web.HttpContext.Current.Session["idstructure"] != null && HttpContext.Current.Session["adminId"] != null)
            {
                string sStructureID = (string)System.Web.HttpContext.Current.Session["idstructure"];
                Thread.CurrentThread.CurrentCulture = CultureInfo.CreateSpecificCulture(HttpContext.Current.Request.UserLanguages[0]);
                string userLanguage = Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName;

                DataSet dsListTarifSessions = new DataSet();
                wsThemisAdmin2010.WSAdminClient wsClt = new wsThemisAdmin2010.WSAdminClient();

                try
                {
                    int postalTel = 0;
                    MyDictionary mySSC = new MyDictionary();
                    mySSC = mySSC.GetDictionaryFromCache(int.Parse(sStructureID));

                    if (mySSC != null && mySSC["VARIABLESPORTABLE"] != null) //sinon si c'est active dans le fichier de config.ini
                    {
                        int.TryParse(mySSC["VARIABLESPORTABLE"], out postalTel);
                    }

                    log.DebugFormat("Appel du Webservice GetFullDetail avec structure : {0} ", sStructureID);

                    //Ajout de la config du tel qui se trouve dans le config.ini
                    dsListTarifSessions = wsClt.GetStatementCashFullDetail(sStructureID, eventId, sessionId, userLanguage, postalTel, _lstTarifs.ToArray());

                    return ConvertDataTableToHTML(dsListTarifSessions.Tables[0]);

                }
                catch (Exception ex)
                {
                    log.Error("catch GetFullDetail du web service " + ex.Message);
                    throw new Exception(ex.Message);
                }
                finally
                {
                    if (wsClt.State == System.ServiceModel.CommunicationState.Opened)
                        wsClt.Close();
                }
            }

            return "danger:aucune_structure_selectionnee";
        }


        [WebMethod(EnableSession = true)]
        public static string GetDetailOfManifAndSeance(int eventId, int sessionId, List<string> _lstEtatsName, List<int> _lstTarifs)
        {
            MyConfigurationManager.SetUserLanguage();

            if (System.Web.HttpContext.Current.Session["idstructure"] != null && HttpContext.Current.Session["adminId"] != null)
            {
                string sStructureID = (string)System.Web.HttpContext.Current.Session["idstructure"];
                Thread.CurrentThread.CurrentCulture = CultureInfo.CreateSpecificCulture(HttpContext.Current.Request.UserLanguages[0]);
                string userLanguage = Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName;

                DataSet dsListTarifSessions = new DataSet();
                wsThemisAdmin2010.WSAdminClient wsClt = new wsThemisAdmin2010.WSAdminClient();

                try
                {
                    log.DebugFormat("Appel du Webservice GetListeSessions avec structure : {0} ", sStructureID);

                    dsListTarifSessions = wsClt.GetStatementCashDetail(sStructureID, eventId, sessionId, userLanguage, _lstEtatsName.ToArray(), _lstTarifs.ToArray());


                    return ConvertDataTableToHTML(dsListTarifSessions.Tables[0]);

                }
                catch (Exception ex)
                {

                    log.Error("catch GetListeSessions du web service " + ex.Message);
                    throw new Exception(ex.Message);
                }
                finally
                {
                    if (wsClt.State == System.ServiceModel.CommunicationState.Opened)
                        wsClt.Close();
                }
            }

            return "danger:aucune_structure_selectionnee";
        }

        public static string GetSelectOfThead2(DataTable dt)
        {
            StringBuilder strbuilder = new StringBuilder();
            strbuilder.Append("<div class='row'><div class='col-12'>");
            strbuilder.Append("<select class=\"form-select\" multiple id=\"selectExportDetailFormat\">");
            for (int i = 0; i < dt.Columns.Count; i++)
            {
                strbuilder.Append("<option value='"+ dt.Columns[i].ColumnName + "' data-trad='" + dt.Columns[i].ColumnName + "'>" + HttpUtility.HtmlDecode(dt.Columns[i].ColumnName) + "</option>");
            }
            strbuilder.Append("</select>");
            strbuilder.Append("</div></div>");

            return strbuilder.ToString();
        }
        public static string GetSelectOfThead(DataTable dt)
        {
            string strbuilder = "<div class='row mb-3'><div class='col-xs-6'>";
            strbuilder += "<select class='form-control chosen-select' multiple='multiple' id='selectExportDetailFormat'>";
            for (int i = 0; i < dt.Columns.Count; i++)
            {
                strbuilder += "<option value='" + dt.Columns[i].ColumnName + "' >" + HttpUtility.HtmlDecode(dt.Columns[i].ColumnName) + "</option>";
                //strbuilder += "<option value='" + dt.Columns[i].ColumnName + "' data-trad='" + dt.Columns[i].ColumnName + "'>" + HttpUtility.HtmlDecode(dt.Columns[i].ColumnName) + "</option>";
            }
            strbuilder += "</select>";
            strbuilder +="</div></div>";

            return strbuilder.ToString();
        }

        public static string ConvertDataTableToHTML(DataTable dt)
        {
            StringBuilder strbuilder = new StringBuilder();

            strbuilder.Append("<div class='row' id='divBtnExportDetail'><div class='col'>");
            strbuilder.Append("<button class='btn btn-primary' id='btnPdfExportDetail'><span data-trad='btn_download_statement_cash_detail'>Télécharger</span></button></div>");
            strbuilder.Append("</div>");

            strbuilder.Append(GetSelectOfThead(dt));

            strbuilder.Append("<div class='table-responsive'><table class='table'>");

            // Add header row
            strbuilder.Append("<thead><tr>");
            for (int i = 0; i < dt.Columns.Count; i++)
            {
                strbuilder.Append("<td data-trad='" + dt.Columns[i].ColumnName + "'>" + HttpUtility.HtmlDecode(dt.Columns[i].ColumnName) + "</td>");
            }
            strbuilder.Append("</tr></thead><tbody>");

            // Add rows
            foreach (DataRow row in dt.Rows)
            {
                strbuilder.Append("<tr>");
                foreach (DataColumn col in dt.Columns)
                {
                    strbuilder.Append("<td>" + row[col.ColumnName].ToString().Replace("'", "''") + "</td>");
                }
                strbuilder.Append("</tr>");
            }

            strbuilder.Append("</tbody></table></div>");

            return strbuilder.ToString();
        }



        public static string ConvertDataTableToHTMLOld(DataTable dt)
        {

            string html = "<div class='row' id='divBtnExportDetail'><div class='col'>";
            html += "<button class='btn btn-primary' id='btnPdfExportDetail'><span data-trad='btn_download_statement_cash_detail'>Télécharger</span></button></div>";
            html += "</div>";


            html += "<div class='table-responsive'><table class='table'>";
            //add header row
            html += "<thead><tr>";
            for (int i = 0; i < dt.Columns.Count; i++)
                html += "<td data-trad='" + dt.Columns[i].ColumnName + "'>" + HttpUtility.HtmlDecode(dt.Columns[i].ColumnName) + "</td>";
            html += "</tr></thead><tbody>";
            //add rows
            /*
            int cpti = 0;
            int cptj = 0;

            int cptiMax = dt.Rows.Count-1;
            int cptjMax = dt.Columns.Count-1;

            foreach (var row in dt.Rows)
            {
                html += "<tr>";
                foreach (var col in dt.Columns)
                {
                    html += "<td>" + dt.Rows[cpti][cptj].ToString() + "</td>";


                    if (cptj < cptjMax)
                    {
                        cptj++;
                    }
                    
                }
                html += "</tr>";


                if(cpti < cptiMax)
                {
                    cpti++;
                }
            }
            */


            StringBuilder strbuilder = new StringBuilder();
            foreach (DataColumn col in dt.Columns)
            {
                strbuilder.Append("<tr>");
                html += "<tr>";
                foreach (DataRow row in dt.Rows)
                {

                   // html += "<td>" + row[col.ColumnName].ToString() + "</td>";
                    strbuilder.Append($"<td>{row[col.ColumnName].ToString().Replace("'","''")}</td>");                   
                }
                strbuilder.Append("</tr>");
                //html += "</tr>";

            }

            html += strbuilder.ToString();
            /*

            for (int i = 0; i < dt.Rows.Count; i++)
            {
                html += "<tr>";
                for (int j = 0; j < dt.Columns.Count; j++)
                    //if(dt.Columns[i].ColumnName.Contains("Montant"))
                    html += "<td>" + dt.Rows[i][j].ToString() + "</td>";
                html += "</tr>";
            }

            */
            html += "</tbody></table></div>";
            return html;
        }

        [WebMethod(EnableSession = true)]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static string DownloadExcelFile(string resume, string tables, string[] selectedCols)
        {
            MyConfigurationManager.SetUserLanguage();

            log.Info("Download Excel File");
            string excelName = ""; string excelpath = "";
            string currentUrl = Path.GetFileNameWithoutExtension(HttpContext.Current.Request.PhysicalPath);
            string strLastStructureId = (string)System.Web.HttpContext.Current.Session["idstructure"];

            if (System.Web.HttpContext.Current.Session["idstructure"] != null)
            {


                string strdminId = HttpContext.Current.Session["adminId"].ToString();
                string strdminName = HttpContext.Current.Session["adminName"].ToString();
                DateTime dt = DateTime.Now;
                excelName = strLastStructureId + "_" + strdminId + "_" + dt.ToString("yyyyMMddHHmmss") + "_" + currentUrl + ".xlsx";
                // excelpath = HttpContext.Current.Request.PhysicalApplicationPath + "pages\\excels\\" + excelName;


                excelpath = MyConfigurationManager.AppSettings("ExcelsFileDownloadStatsEntreesCompletePath").Replace("[idstructure]", strLastStructureId);
                log.Debug("excelpath " + excelpath);


                if (!Directory.Exists(excelpath))
                    Directory.CreateDirectory(excelpath);

             //   excelpath = Path.GetDirectoryName(excelpath) + "\\" + excelName;
                excelpath = excelpath + "\\" + excelName;

                string str = "<body>";

                str += resume;
                str += tables;

                str += "</body>";


                //str = str.Replace("&39;", "'").Replace(".", ",");
                str = str.Replace("&39;", "'");


                var xmlDoc = new XmlDocument();
                xmlDoc.LoadXml(str);


                //  excelName = XmlToExcel(doc, excelpath);
                excelName = CreateExcelDoc(xmlDoc, Path.GetFullPath(excelpath), selectedCols);
                

            }

            log.Debug("return : " + MyConfigurationManager.AppSettings("ExcelsFileDownloadStatsEntrees").Replace("[idstructure]", strLastStructureId) + excelName);
            return MyConfigurationManager.AppSettings("ExcelsFileDownloadStatsEntrees").Replace("[idstructure]", strLastStructureId) + excelName;

            // return excelName;

        }


        public static string CreateExcelDoc(XmlDocument xmlDoc, string excelPath, string[] selectedCols)
        {
            // Si aucune colonne spécifiée, prendre toutes les colonnes du <thead>
            if (selectedCols == null || selectedCols.Length == 0)
            {
                var headerNode = xmlDoc.SelectSingleNode("//table/thead/tr");
                var headerCells = headerNode.SelectNodes("td");
                selectedCols = headerCells.Cast<XmlNode>()
                                          .Select(n => n.InnerText)
                                          .ToArray();
            }

            using (var document = SpreadsheetDocument.Create(excelPath, SpreadsheetDocumentType.Workbook))
            {
                var wbPart = document.AddWorkbookPart();
                wbPart.Workbook = new Workbook();
                var wsPart = wbPart.AddNewPart<WorksheetPart>();
                wsPart.Worksheet = new Worksheet();
                var sheets = wbPart.Workbook.AppendChild(new Sheets());
                var sheet = new Sheet { Id = wbPart.GetIdOfPart(wsPart), SheetId = 1, Name = "Export" };
                sheets.Append(sheet);
                wbPart.Workbook.Save();
                var sheetData = wsPart.Worksheet.AppendChild(new SheetData());

                // 4.1 Identifier les indices des colonnes
                var headerNode = xmlDoc.SelectSingleNode("//table/thead/tr");
                var allHeaderCells = headerNode.SelectNodes("td");
                var indexes = new List<int>();
                for (int i = 0; i < allHeaderCells.Count; i++)
                {
                    var colName = allHeaderCells[i].InnerText;
                    if (selectedCols.Contains(colName))
                        indexes.Add(i);
                }

                // 4.2 Ajouter l'en-tête filtré
                var headerRow = new Row();
                foreach (int idx in indexes)
                {
                    var text = allHeaderCells[idx].InnerText;
                    headerRow.Append(ConstructCell(text, CellValues.String));
                }
                sheetData.AppendChild(headerRow);

                var fr = CultureInfo.GetCultureInfo("fr-FR");
                // 4.3 Corps du tableau
                var bodyRows = xmlDoc.SelectNodes("//table/tbody/tr");
                foreach (XmlNode tr in bodyRows)
                {
                    var row = new Row();
                    var cells = tr.SelectNodes("td");
                    foreach (int idx in indexes)
                    {
                        string raw = cells[idx].InnerText;
                        if (decimal.TryParse(raw, NumberStyles.Number, fr, out decimal decVal))
                        {
                            string formatted = decVal.ToString("F2", CultureInfo.InvariantCulture);
                            row.Append(ConstructCell(formatted, CellValues.Number));
                        }
                        else if (DateTime.TryParse(raw, out DateTime dtVal))
                        {
                            row.Append(ConstructCell(dtVal.ToString("yyyy-MM-dd"), CellValues.String));
                        }
                        else
                        {
                            row.Append(ConstructCell(raw, CellValues.String));
                        }
                    }
                    sheetData.AppendChild(row);
                }

                // 4.4 Pied de page (si présent)
                var footRows = xmlDoc.SelectNodes("//table/tfoot/tr");
                foreach (XmlNode tr in footRows)
                {
                    var row = new Row();
                    var cells = tr.SelectNodes("td");
                    foreach (int idx in indexes)
                    {
                        var text = cells[idx].InnerText;
                        row.Append(ConstructCell(text, CellValues.String));
                    }
                    sheetData.AppendChild(row);
                }

                wsPart.Worksheet.Save();
            }
            return Path.GetFileName(excelPath);
        }


        public static string CreateExcelDocOld(XDocument xDoc, string excelpath)
        {


            log.Debug("==== CreateExcelDoc ====");
            log.Debug("excelpath : " + excelpath);
            using (SpreadsheetDocument document = SpreadsheetDocument.Create(excelpath, SpreadsheetDocumentType.Workbook))
            {
                WorkbookPart workbookPart = document.AddWorkbookPart();
                workbookPart.Workbook = new Workbook();

                WorksheetPart worksheetPart = workbookPart.AddNewPart<WorksheetPart>();
                worksheetPart.Worksheet = new Worksheet();

                Sheets sheets = workbookPart.Workbook.AppendChild(new Sheets());

                Sheet sheet = new Sheet() { Id = workbookPart.GetIdOfPart(worksheetPart), SheetId = 1, Name = "Employees" };

                sheets.Append(sheet);

                workbookPart.Workbook.Save();

          //      List<Employee> employees = Employees.EmployeesList;


                var xmlDoc = new XmlDocument();
                using (var xmlReader = xDoc.CreateReader())
                {
                    xmlDoc.Load(xmlReader);
                }

                SheetData sheetData = worksheetPart.Worksheet.AppendChild(new SheetData());

              
          

                XmlNodeList xmlNodeDivs = xmlDoc.SelectNodes("//div[@class='widget-main']");
                foreach (XmlNode xmlnDiv in xmlNodeDivs)
                {   

                    // Constructing header
                    Row row = new Row();
                    foreach (XmlNode xmlnp in xmlnDiv.SelectNodes("p"))
                    {
                        row.Append(ConstructCell(xmlnp.InnerText, CellValues.String));
                    
                    }

                    // Insert the header row to the Sheet Data
                    sheetData.AppendChild(row);

                }

                XmlNodeList xmlNodeTables = xmlDoc.SelectNodes("//table");
                foreach (XmlNode xmlnTable in xmlNodeTables)
                {
                    // entete
                    XmlNodeList xmlnTrs = xmlnTable.SelectNodes("thead//tr");
                    foreach (XmlNode xmlnTr in xmlnTrs)
                    {
                        Row row = new Row();
               
                        foreach (XmlNode xmlnTd in xmlnTr.SelectNodes("td"))
                        {
                            row.Append(ConstructCell(xmlnTd.InnerText, CellValues.String));

                        }
                        sheetData.AppendChild(row);

                    }

                    // body
                    XmlNodeList xmlnTrbs = xmlnTable.SelectNodes("tbody//tr");
                    foreach (XmlNode xmlnTrb in xmlnTrbs)
                    {
                        Row row = new Row();
                        foreach (XmlNode xmlnTd in xmlnTrb.SelectNodes("td"))
                        {
                            row.Append(ConstructCell(xmlnTd.InnerText, CellValues.String));
                        }
                        sheetData.AppendChild(row);

                    }

                    // footer
                    XmlNodeList xmlnTrfs = xmlnTable.SelectNodes("tfoot//tr");
                    foreach (XmlNode xmlnTr in xmlnTrfs)
                    {
                        Row row = new Row();

                        foreach (XmlNode xmlnTd in xmlnTr.SelectNodes("td"))
                        {
                            row.Append(ConstructCell(xmlnTd.InnerText, CellValues.String));
                        }
                        sheetData.AppendChild(row);

                    }
                }

                worksheetPart.Worksheet.Save();
            }

            return Path.GetFileName(excelpath);
        }

        private static Cell ConstructCell(string value, CellValues dataType)
        {
            return new Cell()
            {
                CellValue = new CellValue(value),
                DataType = new EnumValue<CellValues>(dataType)
            };
        }
        private static string XmlToExcel(XDocument xDoc, string excelpath)
        {

            var stream = new MemoryStream();
            var xl = SpreadsheetDocument.Create(excelpath, SpreadsheetDocumentType.Workbook);



            WorkbookPart wbp = xl.AddWorkbookPart();
            WorksheetPart wsp = wbp.AddNewPart<WorksheetPart>();


            Workbook wb = new Workbook();
            FileVersion fv = new FileVersion();
            fv.ApplicationName = "Microsoft Office Excel";
            Worksheet ws = new Worksheet();

            SheetData sd = new SheetData();

            Row r1 = new Row() { RowIndex = (UInt32Value)1u };
            Cell c1 = new Cell();
            c1.DataType = CellValues.String;
            c1.CellValue = new CellValue("Some value");
            r1.Append(c1);


            Cell c2 = new Cell();
            c2.DataType = CellValues.String;
            c2.CellValue = new CellValue("Other Some value");
            r1.Append(c2);
            sd.Append(r1);

            Row r2 = new Row() { RowIndex = (UInt32Value)1u };
            Cell c3 = new Cell();
            c3.DataType = CellValues.String;
            c3.CellValue = new CellValue("Other Some value");
            r2.Append(c3);

            sd.Append(r2);


            /*
           int ColumnIndex = 1;
           int RowIndex = 1;
           var xmlDoc = new XmlDocument();
           using (var xmlReader = xDoc.CreateReader())
           {
               xmlDoc.Load(xmlReader);
           }

           XmlNodeList xmlNodeDivs = xmlDoc.SelectNodes("//div[@class='widget-main']");
           foreach (XmlNode xmlnDiv in xmlNodeDivs)
           {
               Row r1 = new Row() { RowIndex = (UInt32Value)1u };

               ColumnIndex = 1;
               foreach (XmlNode xmlnp in xmlnDiv.SelectNodes("p"))
               {

                   Cell c1 = new Cell();
                   c1.DataType = CellValues.String;
                   c1.CellValue = new CellValue(xmlnp.InnerText);

                   r1.Append(c1);


                   // xlWorkSheet.Cells[RowIndex, ColumnIndex] = xmlnp.InnerText;
                   ColumnIndex++;
               }
               sd.Append(r1);
               RowIndex++;

           }
            


           
           XmlNodeList xmlNodeTables = xmlDoc.SelectNodes("//table");
           foreach (XmlNode xmlnTable in xmlNodeTables)
           {
               Row r2 = new Row() { RowIndex = (UInt32Value)1u };

               // entete
               XmlNodeList xmlnTrs = xmlnTable.SelectNodes("thead//tr");
               foreach (XmlNode xmlnTr in xmlnTrs)
               {
                   ColumnIndex = 1;
                   foreach (XmlNode xmlnTd in xmlnTr.SelectNodes("td"))
                   {
                       Cell c2 = new Cell();
                       c2.DataType = CellValues.String;
                       c2.CellValue = new CellValue(xmlnTd.InnerText);

                       //   xlWorkSheet.Cells[RowIndex, ColumnIndex] = xmlnTd.InnerText;

                       r2.Append(c2);

                       ColumnIndex++;
                   }
                   sd.Append(r2);
                   RowIndex++;

               }

               // body
               XmlNodeList xmlnTrbs = xmlnTable.SelectNodes("tbody//tr");
               foreach (XmlNode xmlnTrb in xmlnTrbs)
               {
                   Row r3 = new Row() { RowIndex = (UInt32Value)1u };

                   ColumnIndex = 1;
                   foreach (XmlNode xmlnTd in xmlnTrb.SelectNodes("td"))
                   {
                       Cell c3 = new Cell();
                       c3.DataType = CellValues.String;
                       c3.CellValue = new CellValue(xmlnTd.InnerText);

                       // xlWorkSheet.Cells[RowIndex, ColumnIndex] = xmlnTd.InnerText;

                       r3.Append(c3);
                       ColumnIndex++;

                   }
                   sd.Append(r3);
                   RowIndex++;

               }

               // footer
               XmlNodeList xmlnTrfs = xmlnTable.SelectNodes("tfoot//tr");
               foreach (XmlNode xmlnTr in xmlnTrfs)
               {
                   Row r4 = new Row() { RowIndex = (UInt32Value)1u };

                   ColumnIndex = 1;
                   foreach (XmlNode xmlnTd in xmlnTr.SelectNodes("td"))
                   {
                       Cell c4 = new Cell();
                       c4.DataType = CellValues.String;
                       c4.CellValue = new CellValue(xmlnTd.InnerText);


                       r4.Append(c4);
                       //  xlWorkSheet.Cells[RowIndex, ColumnIndex] = xmlnTd.InnerText;
                       ColumnIndex++;
                   }
                   sd.Append(r4);
                   RowIndex++;

               }
           }*/


            wsp.Worksheet = ws;
            wsp.Worksheet.Save();
            Sheets sheets = new Sheets();
            Sheet sheet = new Sheet();
            sheet.Name = "Export";
            sheet.SheetId = 1;
            sheet.Id = wbp.GetIdOfPart(wsp);
            sheets.Append(sheet);
            wb.Append(fv);
            wb.Append(sheets);


            xl.WorkbookPart.Workbook = wb;
            xl.WorkbookPart.Workbook.Save();
            xl.Close();



            /*   xlWorkBook.SaveAs(excelpath);
               xlWorkBook.Close(false);
               xlApp = null;*/

            return Path.GetFileName(excelpath);

        }

        private static string XmlToExcelOld(XDocument xDoc, string excelpath)
        {

            log.Debug("==== XmlToExcel start ====");

            object misValue = System.Reflection.Missing.Value;


            Excel.Workbook xlWorkBook;
            Excel.Worksheet xlWorkSheet;

            Microsoft.Office.Interop.Excel.Application xlApp = new Microsoft.Office.Interop.Excel.Application();
            xlApp.Visible = false;
            xlApp.DisplayAlerts = true;
            xlWorkBook = xlApp.Workbooks.Add();
            xlWorkSheet = (Excel.Worksheet)xlWorkBook.Worksheets.get_Item(1);


            log.Debug("instancie la classe Microsoft.Office.Interop.Excel ");
            int ColumnIndex = 1;
            int RowIndex = 1;
            var xmlDoc = new XmlDocument();
            using (var xmlReader = xDoc.CreateReader())
            {
                xmlDoc.Load(xmlReader);
            }

            XmlNodeList xmlNodeDivs = xmlDoc.SelectNodes("//div[@class='widget-main']");
            foreach (XmlNode xmlnDiv in xmlNodeDivs)
            {
                ColumnIndex = 1;
                foreach (XmlNode xmlnp in xmlnDiv.SelectNodes("p"))
                {
                    xlWorkSheet.Cells[RowIndex, ColumnIndex] = xmlnp.InnerText;
                    ColumnIndex++;
                }
                RowIndex++;
            }


            XmlNodeList xmlNodeTables = xmlDoc.SelectNodes("//table");
            foreach (XmlNode xmlnTable in xmlNodeTables)
            {
                // entete
                XmlNodeList xmlnTrs = xmlnTable.SelectNodes("thead//tr");
                foreach (XmlNode xmlnTr in xmlnTrs)
                {
                    ColumnIndex = 1;
                    foreach (XmlNode xmlnTd in xmlnTr.SelectNodes("td"))
                    {

                        xlWorkSheet.Cells[RowIndex, ColumnIndex] = xmlnTd.InnerText;
                        ColumnIndex++;
                    }
                    RowIndex++;

                }

                // body
                XmlNodeList xmlnTrbs = xmlnTable.SelectNodes("tbody//tr");
                foreach (XmlNode xmlnTrb in xmlnTrbs)
                {

                    ColumnIndex = 1;
                    foreach (XmlNode xmlnTd in xmlnTrb.SelectNodes("td"))
                    {

                        xlWorkSheet.Cells[RowIndex, ColumnIndex] = xmlnTd.InnerText;
                        ColumnIndex++;
                    }
                    RowIndex++;

                }

                // footer
                XmlNodeList xmlnTrfs = xmlnTable.SelectNodes("tfoot//tr");
                foreach (XmlNode xmlnTr in xmlnTrfs)
                {
                    ColumnIndex = 1;
                    foreach (XmlNode xmlnTd in xmlnTr.SelectNodes("td"))
                    {

                        xlWorkSheet.Cells[RowIndex, ColumnIndex] = xmlnTd.InnerText;
                        ColumnIndex++;
                    }
                    RowIndex++;
                }
            }


            xlWorkBook.SaveAs(excelpath);
            xlWorkBook.Close(false);
            xlApp = null;

            return Path.GetFileName(excelpath);

        }




        [WebMethod(EnableSession = true)]
        public static string DownloadPDFFile(string resume, string tables)
        {

            MyConfigurationManager.SetUserLanguage();

            string pdfName = ""; string pdfPath = "";
            // Récupérer le chemin physique du site
            string currentUrl = Path.GetFileNameWithoutExtension(HttpContext.Current.Request.PhysicalPath);
            log.Info("Download PDF file currentUrl " + currentUrl);


            if (System.Web.HttpContext.Current.Session["idstructure"] != null)
            {

                try
                {

                    string strLastStructureId = (string)System.Web.HttpContext.Current.Session["idstructure"];
                    string strdminId = HttpContext.Current.Session["adminId"].ToString();
                    string strdminName = HttpContext.Current.Session["adminName"].ToString();
                    pdfName = strLastStructureId + "_" + strdminId + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + currentUrl + ".pdf";

                    // pdfPath = MyConfigurationManager.AppSettings("PdfFileDownloadStatsEntrees").Replace("[idstructure]", strLastStructureId);
                    pdfPath = System.Web.HttpContext.Current.Server.MapPath(MyConfigurationManager.AppSettings("PdfFileDownloadStatsEntrees").Replace("[idstructure]", strLastStructureId));
                    log.Debug("pdfPath " + pdfPath);



                    if (!Directory.Exists(pdfPath))
                    {
                        log.Debug("On créer le chemin " + pdfPath);
                        Directory.CreateDirectory(pdfPath);
                    }

                    pdfPath = Path.GetDirectoryName(pdfPath) + "\\" + pdfName;
                    log.Debug("pdfPath  : " + pdfPath);

                    
                    string str = "<html><body>";

                    log.Debug("Lecture fichier PDFTEMPLATESTATMENTCASH " + HttpContext.Current.Request.UserLanguages[0] + " " + strLastStructureId);
                    //remplace les variables entre [] dans le template PDF
                    str += GestionTemplate.ReadFileTemplates("PDFTEMPLATESTATMENTCASH", HttpContext.Current.Request.UserLanguages[0], strLastStructureId)
                        .Replace("[resume]", resume).Replace("[tables]", tables);

                    str += "</body></html>";
                    /*

                    string str = "<html><body>";

                    str += "<table>";
                    str += " <thead> <tr>   <td>ANNE SOFIE VON OTTER</td> </tr></thead>";
                    str += "  <tbody>";
                        str +="<tr><td> P </td>  <td>ABO PLEIN TARIF</td><td>30.00 €</td><td>1ERE CATEGORIE</td> <td>3</td><td >90.00 €</td></tr>";
                        str +="<tr><td> P </td>  <td>ABOegh PLEIN TARIF</td><td>30.00 €</td><td>1ERE CATEGORIE</td> <td>3</td><td >90.00 €</td></tr>";
                        str += "<tr><td> P </td>  <td>ABOergerg PLEIN TARIF</td><td>30.00 €</td><td>1ERE CATEGORIE</td> <td>3</td><td >90.00 €</td></tr>";
                    str += " </tbody></table> ";

                    str += "</body></html>";

                    */
                    
                    PdfDocument pdf = PdfGenerator.GeneratePdf(str, PdfSharp.PageSize.A4, 20, null, OnStylesheetLoad, OnImageLoadPdfSharp);
                    pdf.Save(pdfPath);

/*
                    PdfDocument pdfDoc = PdfGenerator.GeneratePdf(str, PdfSharp.PageSize.A4);
                    pdfDoc.Save(pdfPath);
                    */


                    /*

                    XDocument xdoc = XDocument.Parse(str);

                    //récupère tous les noeuds imgcollapse dans le header (th) firstAttribute
                    var tdHeaderUseless = from nm in xdoc.Elements("body").Elements("div").Elements("table").Elements("thead").Elements("tr").Elements("td")
                                          where nm.FirstAttribute.Value.Contains("useless")
                                          select nm;
                    if (tdHeaderUseless != null)
                        tdHeaderUseless.Remove();


                    
                    Document document = new Document();
                    PdfWriter.GetInstance(document, new FileStream(pdfPath, FileMode.Create));
                    document.Open();
                    HTMLWorker hw = new HTMLWorker(document);
                    hw.Parse(new StringReader(xdoc.ToString()));
                    document.Close();
                    
                    */


                    // retour le nom du ficher
                    //return strLastStructureId+"\\"+pdfName;
                    log.Debug("return : " + MyConfigurationManager.AppSettings("PdfFileDownloadStatsEntrees").Replace("[idstructure]", strLastStructureId) + pdfName);
                    return MyConfigurationManager.AppSettings("PdfFileDownloadStatsEntrees").Replace("[idstructure]", strLastStructureId) + pdfName;

                }
                catch (Exception ex)
                {
                    log.Debug("catch DownloadPDFFile : " + ex.Message);
                    throw new Exception("catch DownloadPDFFile : " + ex.Message);
                }
            }

            return "danger:aucune_structure_selectionnee";
        }






        [WebMethod(EnableSession = true)]
        public static string DownloadDetailPDFFile(string tables)
        {

            MyConfigurationManager.SetUserLanguage();

            string pdfName = ""; string pdfPath = "";
            // Récupérer le chemin physique du site
            string currentUrl = Path.GetFileNameWithoutExtension(HttpContext.Current.Request.PhysicalPath);
            log.Info("Download PDF file Detail currentUrl " + currentUrl);


            if (System.Web.HttpContext.Current.Session["idstructure"] != null)
            {

                try
                {

                    string strLastStructureId = (string)System.Web.HttpContext.Current.Session["idstructure"];
                    string strdminId = HttpContext.Current.Session["adminId"].ToString();
                    string strdminName = HttpContext.Current.Session["adminName"].ToString();
                    pdfName = strLastStructureId + "_" + strdminId + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + currentUrl + ".pdf";

                    // pdfPath = MyConfigurationManager.AppSettings("PdfFileDownloadStatsEntrees").Replace("[idstructure]", strLastStructureId);
                    pdfPath = System.Web.HttpContext.Current.Server.MapPath(MyConfigurationManager.AppSettings("PdfFileDownloadStatsEntrees").Replace("[idstructure]", strLastStructureId));
                    log.Debug("pdfPath " + pdfPath);

                    if (!Directory.Exists(pdfPath))
                    {
                        log.Debug("On créer le chemin " + pdfPath);
                        Directory.CreateDirectory(pdfPath);
                    }

                    pdfPath = Path.GetDirectoryName(pdfPath) + "\\" + pdfName;
                    log.Debug("pdfPath  : " + pdfPath);


                    string str = "<html><body>";

                    log.Debug("Lecture fichier PDFTEMPLATESTATMENTCASH " + HttpContext.Current.Request.UserLanguages[0] + " " + strLastStructureId);
                    //remplace les variables entre [] dans le template PDF
                    str += GestionTemplate.ReadFileTemplates("PDFTEMPLATESTATMENTCASH", HttpContext.Current.Request.UserLanguages[0], strLastStructureId).Replace("[tables]", tables);

                    str += "</body></html>";
             
                    PdfDocument pdf = PdfGenerator.GeneratePdf(str, PdfSharp.PageSize.A4, 20, null, OnStylesheetLoad, OnImageLoadPdfSharp);
                    pdf.Save(pdfPath);

                    log.Debug("return : " + MyConfigurationManager.AppSettings("PdfFileDownloadStatsEntrees").Replace("[idstructure]", strLastStructureId) + pdfName);
                    return MyConfigurationManager.AppSettings("PdfFileDownloadStatsEntrees").Replace("[idstructure]", strLastStructureId) + pdfName;

                }
                catch (Exception ex)
                {
                    log.Debug("catch DownloadPDFFile : " + ex.Message);
                    throw new Exception("catch DownloadPDFFile : " + ex.Message);
                }
            }

            return "danger:aucune_structure_selectionnee";
        }


        [WebMethod(EnableSession = true)]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static string DownloadDetailExcelFile( string tables, string[] selectedCols)
        {
            MyConfigurationManager.SetUserLanguage();

            log.Info("Download Excel File");
            string excelName = ""; string excelpath = "";
            string currentUrl = Path.GetFileNameWithoutExtension(HttpContext.Current.Request.PhysicalPath);
            string strLastStructureId = (string)System.Web.HttpContext.Current.Session["idstructure"];

            if (System.Web.HttpContext.Current.Session["idstructure"] != null)
            {
                string strdminId = HttpContext.Current.Session["adminId"].ToString();
                string strdminName = HttpContext.Current.Session["adminName"].ToString();
                DateTime dt = DateTime.Now;
                excelName = strLastStructureId + "_" + strdminId + "_" + dt.ToString("yyyyMMddHHmmss") + "_" + currentUrl + ".xlsx";

                excelpath = MyConfigurationManager.AppSettings("ExcelsFileDownloadStatsEntreesCompletePath").Replace("[idstructure]", strLastStructureId);
                log.Debug("excelpath " + excelpath);


                if (!Directory.Exists(excelpath))
                    Directory.CreateDirectory(excelpath);

                excelpath = excelpath + "\\" + excelName;

                string str = "<body>";
                str += tables;
                str += "</body>";
              //  str = str.Replace(".", ",").Replace("&39;","'").Replace("&", "");
                str = str.Replace("&39;","'").Replace("&", "");
               
              //  XDocument doc = XDocument.Parse(str);

              //  var strfinal = doc.ToString();


                var xmlDoc = new XmlDocument();
                xmlDoc.LoadXml(str);

                //  excelName = XmlToExcel(doc, excelpath);
                excelName = CreateExcelDoc(xmlDoc, Path.GetFullPath(excelpath), selectedCols);


            }

            log.Debug("return : " + MyConfigurationManager.AppSettings("ExcelsFileDownloadStatsEntrees").Replace("[idstructure]", strLastStructureId) + excelName);
            return MyConfigurationManager.AppSettings("ExcelsFileDownloadStatsEntrees").Replace("[idstructure]", strLastStructureId) + excelName;

            // return excelName;

        }


        public static void OnImageLoadPdfSharp(object sender, HtmlImageLoadEventArgs e)
        {
            //var imgObj = Image.FromFile(@"C:\Temp\Test.png");
          //  e.Callback(XImage.FromGdiPlusImage(imgObj));
        }

        public static void OnStylesheetLoad(object sender, HtmlStylesheetLoadEventArgs e)
        {
            e.SetStyleSheet = @"h1, h2, h3 { color: navy; font-weight:normal; }";
        }


        public static string ConvertDataTableToHTML(DataTable dtManif, ref List<int> _lstTotalSommEntreesFinal, ref List<decimal> _lstTotalSommFinal)
        {
            decimal totalSomm = 0;
            int totalSommEntrees = 0;

            string htmltableauTotal = "<div class='table-responsive'><table class='table table-bordered '>";
            htmltableauTotal += "<thead><tr><td colspan='2' class='bgGrey'><span data-trad='lbl_sum_entries_for_event'>Somme des entrées pour la manifestation :</span> <strong>" + dtManif.Rows[0]["manifestation_nom"].ToString() + "</strong></td></tr><tr><td class='bgGrey'><span data-trad='lbl_nbr_total_entries'>nbr total entrées</span></td><td class='bgGrey'><span data-trad='lbl_sum_total'>somme totale</span></td></tr></thead>";
            htmltableauTotal += "<tfoot></tfoot><tbody><tr><td>[totalSommEntrees]</td><td class='amount'>[totalSomm]</td></tr></tbody>";
            htmltableauTotal += "</table></div><div class='space-6'></div>";

            List<string> exludeColumns = new List<string>() { "categorie_id", "manif_id", "manifestation_nom", "seance_date_deb", "type_tarif_id", "seance_id" };
            string htmlTemp = "";

            string htmlTrManif = "<tr data-eventid='" + dtManif.Rows[0]["manif_id"].ToString() + "'><td class='bold center' colspan='7' >" + dtManif.Rows[0]["manifestation_nom"].ToString() +
                                 "<span class='pull-right'> <button type='button' class='spdetailmanifseance btn btn-info' data-toggle='modal' data-target='#modal' ><i class='icon-tasks'></i> <span data-trad='detail_cash_manif_seance'>Détail  </button>  " +
                                 "  <button type='button' class='spfulldetail btn btn-info' data-toggle='modal' data-target='#modal' ><i class='icon-tasks'></i> <span data-trad='btn_full_detail'>Détail complet  </button> </span>" +
                                 "</td></tr>";

            int cptSum = 0;
            decimal cptSumGlobal = 0;

            DataView view = new DataView(dtManif);
            DataTable dtdistinctSeances = view.ToTable(true, "seance_id");

            foreach (DataRow drDistinctSeance in dtdistinctSeances.Rows)
            {
                int seance_id = int.Parse(drDistinctSeance["seance_id"].ToString());

                string htmlthisseance = "<div class='table-responsive'><table class='table table-bordered '>";
                string htmlTrSeanceDateDeb = "<tr  data-seanceid='" + seance_id.ToString() + "'><td colspan='6' class='bgGrey'><span data-trad='lbl_seance_du'>Séance du : </span> " + dtManif.Select("seance_id=" + seance_id.ToString())[0]["seance_date_deb"].ToString() + "</td></tr>";

                htmlthisseance += "<thead>" + htmlTrManif + htmlTrSeanceDateDeb;
                htmlthisseance += "<tr class='entetecols'>";

                for (int k = 0; k < dtManif.Columns.Count; k++)
                {
                    if (exludeColumns.Contains(dtManif.Columns[k].ColumnName))
                    {
                        htmlthisseance += "<td class='useless' data-trad='col_" + dtManif.Columns[k].ColumnName + "'>" + HttpUtility.HtmlDecode(dtManif.Columns[k].ColumnName) + "</td>";
                    }
                    else
                    {
                        htmlthisseance += "<td class='bgGrey bold' data-trad='col_" + dtManif.Columns[k].ColumnName + "'>" + HttpUtility.HtmlDecode(dtManif.Columns[k].ColumnName) + "</td>";
                    }
                }

                htmlthisseance += "</tr></thead><tfoot><tr><td colspan='4' class='bold text-right' data-trad='lbl_sum'> Somme : </td><td class='bold'>[SOMME]</td><td class='bold amount'>[SOMMEGLOBAL]</td></tr></tfoot><tbody>";

                cptSum = 0;
                cptSumGlobal = 0;

                DataRow[] tablRows = dtManif.Select("seance_id=" + seance_id.ToString());
                foreach (DataRow drTarif in tablRows)
                {
                    htmlthisseance += "<tr>";

                    for (int j = 0; j < dtManif.Columns.Count; j++)
                    {
                        if (exludeColumns.Contains(dtManif.Columns[j].ColumnName))
                        {
                            htmlthisseance += "<td class='" + dtManif.Columns[j].ColumnName + " useless' style='display:none'>" + drTarif[j].ToString() + "</td>";
                        }
                        else
                        {
                            if (dtManif.Columns[j].ColumnName == "montant1" || dtManif.Columns[j].ColumnName == "sumSeance")
                            {
                                decimal montant = 0;
                                if (decimal.TryParse(drTarif[j].ToString(), out montant))
                                {
                                    htmlthisseance += "<td class='amount " + dtManif.Columns[j].ColumnName + "' data-trad='" + dtManif.Columns[j].ColumnName + "'>" + montant.ToString("F") + "</td>";
                                }
                                else
                                {
                                    htmlthisseance += "<td class='" + dtManif.Columns[j].ColumnName + "' data-trad='" + dtManif.Columns[j].ColumnName + "'>" + drTarif[j].ToString() + "</td>";
                                }
                            }
                            else
                            {
                                htmlthisseance += "<td>" + drTarif[j].ToString() + "</td>";
                            }
                        }
                    }

                    htmlthisseance += "</tr>";

                    cptSum += int.Parse(drTarif["nbrEntrees"].ToString());
                    cptSumGlobal += decimal.Parse(drTarif["sumSeance"].ToString());
                }

                htmlthisseance += "</tbody></table></div>";
                htmlTemp += htmlthisseance.Replace("[SOMME]", cptSum.ToString()).Replace("[SOMMEGLOBAL]", cptSumGlobal.ToString("F"));

                totalSomm += cptSumGlobal;
                totalSommEntrees += cptSum;

                _lstTotalSommFinal.Add(totalSomm);
                _lstTotalSommEntreesFinal.Add(totalSommEntrees);
            }

            htmltableauTotal = htmltableauTotal.Replace("[totalSommEntrees]", totalSommEntrees.ToString()).Replace("[totalSomm]", totalSomm.ToString("F"));

            return htmltableauTotal + htmlTemp;
        }



    }
}

